{"createBiggerGift": "Create a Bigger Gift for any Occasion", "startGroupGift": "Start a Group Gift", "brandsIncluded": "Brands Included", "brandsIncludedCount": "{{count}} Brands Included", "all": "All", "online": "Online", "inStore": "In-Store", "forMyself": "For Myself", "forSomeoneElse": "For Someone Else", "occasionGiftCard": "Select Occasion Gift Card", "continue": "Continue", "change": "Change", "groupGiftSettings": "GroupGift Settings", "giftTitle": "Gift Title (e.g., <PERSON><PERSON>s <PERSON>)", "organizedBy": "Organized by (e.g., Design team or Class 5B)", "noteToContribute": "Note to Contributors", "viewExamples": "View Examples", "moreSettings": "More Settings", "noteToContributePlaceholder": "Add a note telling more about this group gift or add any other detail. This note will be shown to all the invitees.", "receiverAndDelivery": "Receiver & Delivery", "receiverName": "Receiver’s Name", "receiverEmail": "Receiver <PERSON><PERSON>", "selectDate": "Select Date", "create": "Create", "giftDelivery": "Gift Delivery", "optional": "(Optional)", "previous": "Previous", "next": "Next", "close": "Close", "useThisMessage": "Use This Message", "viewExampleTitle": "Simply insert to get started, then edit to make it personal", "of": "of", "seeMore": "See More", "searchBrand": "Search brands here", "myGroupGift": "My Group Gifts", "invited": "Invited", "yourContribution": "Your Contribution", "totalContribution": "Total Contribution", "from": "From", "deliveryOn": "Delivery On", "viewContributors": "View {{count}} Contributors", "sent": "<PERSON><PERSON>", "deleted": "Deleted", "cancelled": "Cancelled", "open": "Open", "inviteFriends": "Invite Friends", "contribute": "Contribute", "changeCard": "Change card", "closeSend": "Close & Send", "delete": "Delete", "switchLanguage": "العربية", "sendViaEmailOrSms": "You can send via email or SMS or both!", "organizedByErrorMessage": "Organized by should not be empty", "titleErrorMessage": "Title of your gift should not be empty", "invalidPhoneNumber": "Enter a valid Mobile Number", "phoneNumberRequired": "Phone number is required", "nameRequired": "Name is required", "invalidEmailAddress": "Enter a valid email address.", "searchHere": "Search here...", "gmt": "GMT", "time": "Time", "organizedByText": "Organized by", "noInstoreDataAvailable": "No \"In-Store\" brands available.", "noOnlineDataAvailable": "No \"Online\" brands available.", "seeLess": "See Less", "availableIn": "Available in", "downloadOurApp": "Download Our App", "downloadTheApp": "Download our App to enjoy lots of exciting features", "buyEGiftCards": "Buy eGift Cards", "logOut": "Log out", "helpline": "Helpline", "hey": "Hey", "account": "Account", "loginOrSignUp": "Login / Sign Up", "selectStore": "Select Store", "startAGroupGift": "Start a Group Gift", "viewMyGg": "View My Group Gifts", "invitedText": "{{name}} has invited you to contribute to a GroupGift.", "specialNote": "Note from", "clear": "Clear", "deliverTo": "Deliver to", "viewMyGroupGift": "View My Group Gifts", "cancel": "Cancel", "ok": "OK", "save": "Save", "done": "Done", "areYouSure": "Are you sure?", "closeSendSummary": "This will close and send the group gift with all the contributed amounts to the receiver. No more contributions will be accepted for this group gift after this step.", "sendNow": "Send Now", "deleteGroupGift": "Delete Group Gift", "deleteSummary": "This will permanently delete the group gift and cannot be reversed. Contributions made will be refunded within 7-14 business days. Do you want to delete?", "groupgiftFor": "Group Gift for", "amountValidationMsg": "Please provide a valid Amount", "skipContribution": "Skip Contribution", "skipStep": "Skip this Step", "suggestedText": "The Organizer suggests to contribute {{currency}}", "fixedText": "The Organizer has set the contribution amount of {{currency}}", "custom": "CUSTOM", "otherAmount": "Other Amount", "personalize": "Personalize", "yourMessage": "Your Message", "edit": "Edit", "discard": "Discard", "photoOrVideo": "Photo or Video", "addPhoto": "Add Photo", "addVideo": "Add Video", "recordFromCamera": "Record from Camera", "uploadAFile": "Upload a file", "addGif": "Add GIF", "pleaseWait": "Please wait.", "uploadingVideo": "Uploading...", "uploading": "Uploading...", "uploaded": "Uploaded", "processingVideo": "Processing...", "limitReached": "File uploaded is too large", "photoFileSizeReached": "Please upload files up to 15 MB", "photoFileSizeWarn": "Please upload files up to 5 MB", "videoFileSizeWarn": "The file is {{size}} MB exceeding the maximum file size of 100 MB and the duration should be less than 30 seconds.", "videoDurationWarn": "Video duration should be less than 30 seconds.", "usermessage": "Enter your message here.", "characters": "Characters", "proceedToPay": "Proceed to Pay", "charLimit": "Character Limit", "charLimitMessage": "Please restrict your message between 2 to 720 characters", "contributeNow": "Contribute Now", "deliver": "Deliver", "giftingTo": "Gifting to", "preview": "Preview", "confirm": "Confirm", "noGretingCover": "No greeting cover available !", "errorTitle": "Uh Oh!", "organizer": "Organizer", "FieldErrorMsg": "Minimum 2 characters required", "successfullyContributed": "Successfully Contributed", "downloadApp": "Download YOUGotaGift App", "oops": "Oops!", "paymentFailed": "Payment Failed!", "backToGroupGift": "Back to GroupGift", "tryContributeAgain": "Try contributing again after some time", "networkConnectionError": "This may occur due to bad network connection", "contributeAgain": "You can contribute again", "shareGroupGiftLink": "Share Group Gift link to allow contributions", "listOfContributors": "List of Contributors", "copied": "<PERSON>pied", "gifs": "GIFs", "arabic": "Arabic", "english": "English", "recordAgain": "Record again", "upload": "Upload", "backGroundColor": "Background Color", "searchGif": "Search GIFs here", "contributeGuest": "Contribute as Guest", "or": "Or", "loginSignUp": "Login or Sign Up", "loginText": "Login to YOUGotaGift to keep track of Group Gift - {{name}}", "enterDetails": "Enter your details", "name": "Name", "email": "Email", "scheduledOn": "Scheduled On", "hasInvited": "is organizing a GroupGift for {{recieverName}} & inviting you to contribute.", "egiftCardFor": "It’s an eGift Card for", "guestDescp": "After downloading the app, please click on the invitation link to add the Group Gift to your app", "guestTitle": "Something went wrong!", "guestContents": "Try checking with the organiser for the valid link and try again.", "guestContents2": "Looks like you have entered an invalid link.", "deliveredOn": "Delivered On", "areYouReadyForYourSurprise": "Are you ready for your Surprise Gift?", "viewGreetingsAgain": "View Greetings Again", "skipToGift": "Skip to <PERSON>", "openNow": "Open Now", "organisedBy": "Organised by", "viewContributes": "View contributors", "deliveryInfo1": "You can manually close & send the Group Gift at any point in time.", "deliveryInfo2": "Even If the collection target is not achieved the Gift will be sent on Delivery Date.", "deliveryInfo3": "For contributions exceeding {{amount}}, multiple Gift Cards will be sent.", "success": "Success!", "ggSuccess": "Your Group Gift is created successfully", "ggEditSuccess": "Group Gift Successfully edited.", "targetAmountValidation": "Target Amount should exceed the Suggested/Fixed Amount", "targetMinAmountValidation": "Minimum {{amount}} required", "failed": "Failed!", "reciverMobileNumber": "Receiver Mobile Number", "noResults": "No Results Found !", "errorDesc": "You need to either contribute or personalise the gift to continue", "noContribute": "Something isn't there", "groupGifting": "Group Gifting", "groupGiftingInfo": "Helping you organize a bigger & better gift, together!", "mandatoryError": "This is a mandatory field.", "organiseText": "Organize a bigger gift for any occasion", "organiseDescription": "The easiest way to organize and invite friends to contribute for a bigger and better gift for any occasion", "organizerView": "Organizer View", "brandName": "It's a {{brandName}}", "lastDay": "Hurry! Last day to contribute", "birthday": "Birthday", "newBaby": "New Baby", "teachersDay": "Teacher's Day", "thankYou": "Thank You", "skippedPersonalisation": "You have skipped the personalization!", "addPersonalisation": "Tap here to add your personal touch", "somethingBigger": "Something special coming up? Gift bigger ...", "groupGifts": "Group Gifts", "dontHave": "You don’t have any", "noBrand": "No Brands found for", "spellingError": "Please ensure that there are no spelling errors", "selectTime": "SELECT TIME", "errorSubTitle": "This is not quite right. Don’t worry, let’s get you back on track!", "errorButtonTitle": "Go to Home", "404": "404 - Page not found", "unusualActivityDetected": "Unusual Activity Detected", "unusualActivityDesc": "We have deactivated your account as we have detected unusual activity. Please visit our <strong>Help Centre</strong> to reactivate your account.", "allowHardwareAccess": "Allow access to the camera on this device", "allowHardwareAccessMessage": "If you allow access, people using this device will be able to choose if their apps have camera access by using the settings on this page.", "helloGreeting": "Hello everyone, \n\n", "defaultContribution": "We've set up a group gift fund to contribute towards a special present collectively. Please feel free to contribute using the link below:", "maxCharacterMsg": "Maximum 26 characters allowed", "giftExpiredCon": "GroupGift has expired. Please get in touch with the Organiser.", "giftExpiredOrg": "GroupGift has expired. Kindly create a new GroupGift.", "giftDeletedOrg": "GroupGift has been deleted. Kindly create a new GroupGift.", "giftDeletedCon": "GroupGift has been deleted. Please get in touch with the Organiser.", "giftSended": "GroupGift has already been sent. Please get in touch with the Customer Support Team.", "sentTo": "Sent to", "animated": "Animated", "cookieDesc": "In order for our website to function correctly you must configure your browser to accept cookies", "suspendedInfo1": "Let's get you back online!", "suspendedInfo2": "Please contact our customer support team to reactivate your account.", "accountSuspended": "Account suspended <br/> due to unusual activity", "reactivateAccount": "Reactivate My Account", "contributingAs": "Contributing as", "contributeDescription": "Recipient will see the contribution made under this name", "notEmpty": "Name cannot be empty", "contributedBy": "Contributed by", "for": "For", "celebrateInfo": "Celebrate employees on their personal occasions.", "organizeInfo": "Organize a bigger gift with their work colleagues.", "copyDesc": "Share link with a colleague to Start Group Gift", "fareWell": "<PERSON><PERSON><PERSON>", "congrats": "Congrats", "abbrLang": "AR", "skip": "<PERSON><PERSON>", "successfullyPaid": "Successfully Paid", "spreadingHappiness": "Spreading Happiness", "developersHub": "Developers Hub", "giftCardApiSolutions": "Gift Card APIs & Solutions", "helpCentre": "Help Centre", "haveQuestion": "Have questions?", "support": "Support", "checkOutOur": "check out our", "download": "Download", "yougotagiftApp": "YOUGotaGift App", "forBusiness": "For Business", "forDevelopers": "For Developers", "signIn": "Sign in", "profile": "Profile", "myOrders": "My Orders", "myWallet": "My Wallet", "giftCardSolutions": "Gift Card Solutions", "work": "Work", "login": "<PERSON><PERSON>", "myAccount": "My Account", "gifting": "Gifting", "happyYouOffers": "HappyYOU Offers", "hiring": "Hiring", "downloadapp": "Download App", "privacyPolicy": "Privacy Policy", "new": "New", "termsOfUseOBold": "Terms Of Use", "downloaApp": "Download Our App.", "enjoyExp": "Enjoy a Total Experience."}