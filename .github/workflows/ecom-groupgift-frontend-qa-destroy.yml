name: Destroy QA Deployment ecom-groupgift-frontend
run-name: Destroying ecom-groupgift-frontend ${{ inputs.branch }}

on:
  workflow_dispatch:
    inputs:
      branch:
        description: 'Branch'
        required: true
        type: string

  repository_dispatch:
    types: [ "QA Cleanup" ]
    inputs:
      branch:
        description: 'Branch'
        required: true
        type: string

jobs:
  deploy:
    name: QA
    uses: YouGotaGift/devops-organization-actions/.github/workflows/qa-destroy-frontend-deployed-app.yml@main
    with:
      application_name: ecom-groupgift-frontend
      branch: ${{ inputs.branch || github.event.client_payload.branch }}
    secrets: inherit
