name: QA Automation Deploy ecom-groupgift-frontend

on:
  schedule:
    - cron: '0 18 * * *'

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Trigger QA Build & Deploy ecom-groupgift-frontend workflow
        run: |
          curl -X POST \
            -H "Authorization: Bearer ${{ secrets.GITHUB_TOKEN }}" \
            -H "Accept: application/vnd.github.everest-preview+json" \
            "https://api.github.com/repos/${{ github.repository }}/dispatches" \
            -d '{"event_type": "QA Build & Deploy ecom-groupgift-frontend", "client_payload": { "branch": "<QA_AUTOMATION_BRANCH_NAME>", "action": "deploy" }}'

