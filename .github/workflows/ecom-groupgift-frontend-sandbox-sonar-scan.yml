name: Run SonarScan

on:
  push:
    branches:
      - main
  pull_request:
      types: [opened, synchronize, reopened]
  workflow_dispatch:

jobs:
  sonar-scan:
    name: Sonar-Scanning code
    runs-on: self-hosted
    steps:
     - name: Checkout Code
       uses: actions/checkout@v4.1.1
       with:
         ref: main
     - name: SonarQube Scan
       uses: sonarsource/sonarqube-scan-action@v2.0.1
       env:
         SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
         SONAR_HOST_URL: ${{ secrets.SONAR_INTERNAL_URL }}
       with:
         args: >
           -Dsonar.projectKey=ecom-groupgift-frontend-sandbox
           -sonar.exclusions=**/.github/**,**/deployment/**,**/*.spec.js,**/*.test.js,**/node_modules/**,/node_modules,/.pnp,.pnp.js,.yarn/install-state.gz,/coverage,/.next/,/out/,/build,.DS_Store,*.pem,npm-debug.log*,yarn-debug.log*,yarn-error.log*,.env*.local,.vercel,*.tsbuildinfo,next-env.d.ts,.sonar,.scannerwork

     - name: Waiting for SonarQube to process the result
       run: sleep 15
     - name: Get Result
       env:
         GITHUB_CONTEXT: ${{ toJson(github) }}
       run: |
        response=$(curl -sk ${{ secrets.SONAR_INTERNAL_URL }}/api/qualitygates/project_status?projectKey=ecom-groupgift-frontend-sandbox | jq -r '.projectStatus.status')
        errorThreshold=$(curl -sk ${{ secrets.SONAR_INTERNAL_URL }}/api/qualitygates/project_status?projectKey=ecom-groupgift-frontend-sandbox | jq -r '.projectStatus.conditions | .[] | .errorThreshold')
        actualValue=$(curl -sk ${{ secrets.SONAR_INTERNAL_URL }}/api/qualitygates/project_status?projectKey=ecom-groupgift-frontend-sandbox | jq -r '.projectStatus.conditions | .[] | .actualValue')

        echo "Current threshold: **${errorThreshold}**" >> $GITHUB_STEP_SUMMARY
        echo "Critical issues: **${actualValue}**" >> $GITHUB_STEP_SUMMARY
        if [[ "${response}" == "ERROR" ]] ; then
          echo "**SonarQube quality test Failed!**" >> $GITHUB_STEP_SUMMARY
          echo "Check Sonar Code Analysis report at ${{ vars.SONAR_EXTERNAL_URL }}/dashboard?id=ecom-groupgift-frontend-sandbox" >> $GITHUB_STEP_SUMMARY
          exit 1
        else
          echo "**SonarQube quality test Success!**" >> $GITHUB_STEP_SUMMARY
          echo "Check Sonar Code Analysis report at ${{ vars.SONAR_EXTERNAL_URL }}/dashboard?id=ecom-groupgift-frontend-sandbox" >> $GITHUB_STEP_SUMMARY
        fi

