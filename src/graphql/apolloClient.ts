import { ApolloClient, ApolloLink, InMemoryCache } from '@apollo/client';
import HttpLinks from './httpLinks';
import apolloLogger from 'apollo-link-logger';
import ErrorLink from './errorLink';

// Client-side Apollo Client
export const getClient = (lang: string) => {
  const { currentHttpLink } = HttpLinks(lang);
  const { onErrorLink } = ErrorLink();
  return new ApolloClient({
    cache: new InMemoryCache({
      typePolicies: {
        Query: {
          fields: {
            groupGiftDetail: {
              merge: true,
            },
            footer: {
              merge: true,
            },
          },
        },
      },
    }),
    credentials: 'include',
    defaultOptions: {
      watchQuery: {
        fetchPolicy: 'network-only',
        nextFetchPolicy: 'network-only',
      },
      query: {
        fetchPolicy: 'network-only',
        errorPolicy: 'all',
      },
    },
    link: ApolloLink.from([onErrorLink, apolloLogger, currentHttpLink()]),
  });
};
