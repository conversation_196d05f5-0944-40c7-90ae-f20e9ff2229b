export const dynamic = 'force-dynamic';

import React, { ReactNode } from 'react';
import { imageBaseUrl } from '@/constants/envVariables';
import GiftOpenHeader from '@/features/gifts/giftsOpen/header/GiftOpenHeader';
import { useAppDispatch } from '@/redux/hooks';
import Notifier from '@/features/common/notifier/Notifier';
import Loader from '@/features/common/loader/Loader';
import { useParams } from 'next/navigation';
import Image from 'next/image';
import useGiftOpenSlice from './giftOpenSlice';
import { GIFT_OPEN_SECTION } from './constants/constants';
import { PAGEURLS } from '../constants/gifts.constants';
import Link from 'next/link';
import CustomLink from '@/features/common/customLink/CustomLink';

interface IGiftOpenPageLayout {
  children: ReactNode;
  hideHeader?: boolean;
  reCaptchaKey?: string;
  isNavigation?: boolean;
  isGroupgift?: boolean;
  isLoading?: boolean;
}

const GiftOpenPageLayout = ({ children, isLoading }: IGiftOpenPageLayout) => {
  const dispatch = useAppDispatch();
  const params = useParams();
  // taking public image config url
  const closeButton = `${imageBaseUrl}/images/gift-open/close-circle.svg`;

  // #. Get gift open dispatch
  const { setActiveSection } = useGiftOpenSlice();

  const handleCloseBtnClicked = () => {
    dispatch(setActiveSection(GIFT_OPEN_SECTION.OPEN));
  };

  return (
    <>
      <style jsx>{`
        //children_container_styling
        .gift-open {
          background: #ebeded url('${imageBaseUrl}/images/gift-wrapper.jpg')
            no-repeat center center / cover;
          position: relative;
        }
      `}</style>
      <div className={`gift-open`}>
        <div className={`gift-open__container `}>
          <GiftOpenHeader />

          <section
            className={`gift-open__children}
            `}
          >
            {children}
          </section>
          <Notifier />
          {isLoading && <Loader />}
        </div>
      </div>
      <CustomLink
        hrefLink={`${PAGEURLS.GROUP_GIFTS}/${params?.giftId}${PAGEURLS.CONFIRM}`}
        prefetch={false}
        // legacyBehavior
      >
        <div
          className={`gift-open__close-btn-wrapper`}
          onClick={handleCloseBtnClicked}
        >
          <Image src={closeButton} alt="go back" height={32} width={32} />
        </div>
      </CustomLink>
    </>
  );
};

export default GiftOpenPageLayout;
