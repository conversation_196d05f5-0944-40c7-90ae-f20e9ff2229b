import React, { useState } from 'react';
import styles from './ConfirmRecieverDetails.module.scss';
import Marquee from 'react-fast-marquee';
import SvgIcon from '@/features/common/svgIcon/SvgIcon';
import getRegionAndLocale from '@/utils/getRegionAndLocale';
import Button from '@/features/common/button/Button';
import CircularProgress from '@mui/material/CircularProgress';
import { useTranslation } from 'react-i18next';
import ContributeDrawer from '@/features/common/drawer/ContributeDrawer';
import { getCookie } from '@/utils/getAndsetCookie';
import { IS_GUEST_USER_COOKIE } from '@/constants/common';

/**
 * @method ConfirmRecieverDetails
 * @description confirmation page reciever details component
 * @returns {JSX.Element}
 */
const ConfirmRecieverDetails = ({
  reciever,
  loading,
  handleContritube,
  contributingName,
}: any): JSX.Element => {
  // translations
  const { t } = useTranslation();
  const { locale } = getRegionAndLocale();
  const [open, setOpen] = useState<boolean>(false);

  // #. Get guest cookie
  const isGuestLoginCookie: any = getCookie(IS_GUEST_USER_COOKIE);
  const toggleClick = (value: any) => {
    setOpen(false);
  };

  return (
    <>
      <div className={styles['delivery-details']}>
        {isGuestLoginCookie === 'true' ? (
          <>
            {reciever?.email && (
              <div className={styles['delivery-details__contents']}>
                <span className={styles['confirm-icons']}>
                  <SvgIcon icon="profile" width="24" height="24" />
                </span>
                {reciever?.email?.length > 25 ? (
                  <Marquee
                    speed={30}
                    className={`${styles['marquee']} ${styles['reciever-marquee']}`}
                    direction={locale === 'ar' ? 'right' : 'left'}
                  >
                    <span className={styles['reciever-email']}>{reciever?.email}</span>
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  </Marquee>
                ) : (
                  <span className={styles['reciever-email']}>{reciever?.email}</span>
                )}
              </div>
            )}
            {reciever?.countryCode && (
              <div className={styles['delivery-details__contents']}>
                <span className={styles['confirm-icons']}>
                  <SvgIcon icon="sms-icon" width="24" height="24" />
                </span>
                <span className={styles['reciever-mob']}>
                  {reciever?.countryCode}&nbsp;{reciever?.phoneNumber}
                </span>
              </div>
            )}
          </>
        ) : (
          <div className={styles['delivery-details__contents']}>
            <span className={styles['confirm-icons']}>
              <SvgIcon icon="profile" width="24" height="24" />
            </span>
            <div className={styles['delivery-details__wrapper']}>
              <p className={styles['contributer-text']}>{t('contributedBy')}</p>
              <div style={{ display: 'flex' }}>
                {contributingName?.length > 15 ? (
                  <Marquee
                    speed={30}
                    className={styles['marquee']}
                    direction={locale === 'ar' ? 'right' : 'left'}
                  >
                    <p className={styles['contributer-name']}>{contributingName}</p>
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  </Marquee>
                ) : (
                  <p className={styles['contributer-name']}>{contributingName}</p>
                )}

                <a className={styles['confirm-icons']} onClick={() => setOpen(true)}>
                  <SvgIcon icon="bold-edit" width="24" height="24" />
                </a>
              </div>
            </div>
          </div>
        )}

         {reciever?.scheduledDate && (
          <div className={styles['delivery-details__contents']}>
            <span className={styles['confirm-icons']}>
              <SvgIcon icon="calendar" width="24" height="24" />
            </span>
            <p className={styles['scheduled-date']}>
              {t('deliver')}&nbsp;
              <span style={{ color: '#0E0F0C' }}>
                {reciever?.scheduledDate}
              </span>
            </p>
          </div>
        )} 
      </div>
      <div className={`${styles['btn-wrapper']} circular-wrapper`}>
        <Button
          theme="primary"
          className={`${styles['btn-wrapper__continue']}`}
          action={handleContritube}
          attribues={{ disabled: loading ? true : false }}
        >
          {loading ? <CircularProgress /> : t('contributeNow')}
        </Button>
      </div>

      {open && (
        <div id="drawer-container" style={{ position: 'relative' }}>
          <ContributeDrawer
            open={open}
            onClose={() => toggleClick(false)}
            contributingName={contributingName}
          />
        </div>
      )}
    </>
  );
};

export default ConfirmRecieverDetails;
