@import '@/styles/abstracts/abstracts';

.delivery-details {
  margin-top: 24px;
  background: $medium-light-grey;
  padding: 16px 8px;
  border-radius: 8px;
  display: flex;
  gap: 10px;
  flex-direction: column;
  color: $dark-charcoal;

  .confirm-icons{
    width: 24px;
    height: 24px;
  }

  &__contents {
    display: flex;
    gap: 8px;

    span {

      @include rtl-styles {
        unicode-bidi: plaintext;
      }
    }

    .reciever-email,.reciever-mob{
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
      color: $dark-charcoal;
    }

    .scheduled-date{
      font-size: 14px;
      font-weight: 500;
      line-height: 18px;
      letter-spacing: -0.14px;
      color: $text-grey;
      span{
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
      }
    }
  }

  &__wrapper {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 3px;

    .contributer-text{
      font-size: 14px;
      font-weight: 500;
      line-height: 18px;
      letter-spacing: -0.14px;
      color: $text-grey;
    }

    .contributer-name{
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
      width: 166px;
    }

    a {
      margin-left: 7px;

      @include rtl-styles {
        margin-right: 7px;
        margin-left: 0;
      }
    }
  }
}

.btn-wrapper {
  margin-top: 16px;

  &__continue{
    height: 50px;
    font-size: 16px;
    font-weight: 700;
    line-height: 24px;
    letter-spacing: -0.16px;
    span{
      display: inline-block;
    }
  }
}

.marquee {
  direction: ltr !important;
  scrollbar-width: none;
  width: 166px !important;

  &::-webkit-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
    display: none;
    width: 0px;
  }
}

.reciever-marquee {
  width: 100% !important;
}