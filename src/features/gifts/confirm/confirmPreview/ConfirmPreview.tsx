import styles from './ConfirmPreview.module.scss';
import Image from 'next/image';
import {
  CONFIRM_TYPES,
  FONT_FAMILIES,
  PAGEURLS,
} from '../../constants/gifts.constants';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import { useTranslation } from 'react-i18next';
import CustomLink from '@/features/common/customLink/CustomLink';

const ConfirmPreview = ({
  type,
  personalisationData,
}: {
  type: string;
  personalisationData: any;
}) => {
  const { t } = useTranslation();
  const params = useParams();

  //#. setting personlaised datas
  const previewGreeting = personalisationData?.occasion?.greetinghubImageUrl;
  const previewVideoThumb =
    personalisationData?.personalisation?.video?.videoInfo?.medias?.thumb &&
    `https:${personalisationData?.personalisation?.video?.videoInfo?.medias?.thumb}`;
  const previewImage = personalisationData?.personalisation?.photo?.file;
  const previewMessage = personalisationData?.message?.message;
  const previewGif = personalisationData?.personalisation?.gif?.url;
  const bgColor = personalisationData?.message?.backgroundColor;
  const fontFamily = personalisationData?.message?.fontFamily;

  let selectedFont;
  switch (fontFamily) {
    case FONT_FAMILIES.POPPINS:
      selectedFont = 'var(--font-poppins)';
      break;
    case FONT_FAMILIES.GEORGIA:
      selectedFont = 'Georgia';
      break;
    case FONT_FAMILIES.ARIAL:
      selectedFont = 'Arial';
      break;
    case FONT_FAMILIES.BRADLEY:
      selectedFont = 'Bradley Hand';
      break;
    case FONT_FAMILIES.BRUSH_SCRIPT:
      selectedFont = 'Brush script MT';
      break;
    case FONT_FAMILIES.BLAKA_HOLLOW:
      selectedFont = 'var(--blaka-hollow)';
      break;
    case FONT_FAMILIES.TAJAWAL:
      selectedFont = 'var(--tajawal)';
      break;
    case FONT_FAMILIES.REEM_KUFI:
      selectedFont = 'var(--reem-kufi)';
      break;
    case FONT_FAMILIES.EL_MESSIRI:
      selectedFont = 'var(--el-messiri)';
      break;
    case FONT_FAMILIES.CAIRO:
      selectedFont = `var(--font-cairo)`;
      break;
    case FONT_FAMILIES.MONA_SANS:
      selectedFont = `var(--font-mona-sans)`;
      break;
    case FONT_FAMILIES.NOTA_KUFI:
      selectedFont = `var(--font-noto-kufi)`;
      break;
  }

  return (
    <>
      {type === CONFIRM_TYPES.PREVIEW && (
        <div className={styles['confirm-preview']}>
          <div className={styles['confirm-preview__card']}>
            {previewGreeting ||
            previewVideoThumb ||
            previewImage ||
            previewGif ? (
              <Image
                src={
                  previewGreeting ||
                  previewGif ||
                  previewVideoThumb ||
                  previewImage
                }
                width={350}
                height={353}
                alt="img"
                className={styles['confirm-preview__card-img']}
              />
            ) : (
              <div
                className={styles['confirm-preview__message']}
                style={{ background: bgColor }}
              >
                <pre style={{ fontFamily: selectedFont }}>{previewMessage}</pre>
              </div>
            )}
            
          </div>
        </div>
      )}

      {type === CONFIRM_TYPES.SKIPPED && (
        <div className={styles['confirm-skipped']}>
          <p>{t('skippedPersonalisation')}</p>
          <CustomLink
            hrefLink={`${PAGEURLS.GROUP_GIFTS}/${params?.giftid}${PAGEURLS.GREETING}`}
          >
            {t('addPersonalisation')}
          </CustomLink>
        </div>
      )}
    </>
  );
};

export default ConfirmPreview;
