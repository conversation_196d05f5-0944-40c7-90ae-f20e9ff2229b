@import '@/styles/abstracts/abstracts';

.confirm-preview {
  &__card {
    height: 353px;
    box-shadow: 0 0 16px 0 rgba(0, 0, 0, 0.12);
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    border: 1px solid white;
    position: relative;

    &-img {
      display: block;
      width: 100%;
      height: auto;
      object-fit: contain;
      border-top-left-radius: 16px;
      border-top-right-radius: 16px;
    }
    .preview-card {
      width: 80px;
      height: 70px;
      box-shadow: -4px 4px 10px 0 rgba(0, 0, 0, 0.15);
      background: $white;
      border-bottom-left-radius: 16px;
      border-top-right-radius: 16px;
      position: absolute;
      top: -3px;
      right: -3px;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      cursor: pointer;
      span {
        font-size: rem(14);
        font-weight: 500;
        color: #f8450b;
      }
    }
  }

  &__message {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    overflow-wrap: break-word;
    pre {
      text-align: center;
      padding: 128px 22px 70px 22px;
      overflow: scroll;
      height: 100%;
      font-size: rem(16);
      font-weight: 500;
      color: #fff;
      scrollbar-width: none;
      margin: 0;
      white-space: break-spaces;

      &::-webkit-scrollbar {
        -ms-overflow-style: none; /* IE and Edge */
        display: none;
        scrollbar-width: none;
        width: 0px;
      }
    }
  }
}

.confirm-skipped {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  height: 353px;
  background: $medium-light-grey;
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
  text-align: center;
  gap: 8px;

  p {
    color: $dark-charcoal;
    font-size: rem(16);
    font-weight: 700;
    line-height: 24px; 
    letter-spacing: -0.16px;
  }
  a {
    color: $barney-purple;
    font-size: rem(14);
    line-height: 18px;
    letter-spacing: -0.14px;
    font-weight: 600;
  }
}
