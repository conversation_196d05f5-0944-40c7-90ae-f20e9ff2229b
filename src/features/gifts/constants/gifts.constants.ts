// #. Message background-colors
export const USER_MESSAGE_BACKGROUNDS = [
  '#7dcba6',
  '#EB6B6B',
  '#1DCAFF',
  '#6B8CFA',
  '#F8C668',
  '#6FDD1F',
  '#C260F5',
  '#F22F98',
  '#808080',
];

// #. Message font-sizes
export const USER_MESSAGE_FONT_SIZES = [
  16, 18, 20, 22, 24, 26, 28, 30, 32, 34, 36,
];

// #. Message English font familys
export const USER_MESSAGE_FONT_FAMILY_EN = [
  'Mona Sans',
  'Poppins',
  'Brush script MT',
  'Georgia',
  'Arial',
  'Bradley Hand',
];

// #. Message Arabic font family
export const USER_MESSAGE_FONT_FAMILY_AR = [
  'Noto Ku<PERSON>',
  'Cairo',
  'Tajawal',
  '<PERSON>',
  '<PERSON><PERSON>',
  'Blaka Hollow',
];

// #. Contribution config default greetings
export const CONTRIBUTION_CONGIF = {
  ILLUSTRATIONS: 'illustrations',
  ANIMATED: 'animated',
};

export const FONT_FAMILIES = {
  POPPINS: 'Poppins',
  BRUSH_SCRIPT: 'Brush script MT',
  GEORGIA: 'Georgia',
  ARIAL: 'Arial',
  BRADLEY: 'Bradley Hand',
  CAIRO: 'Cairo',
  TAJAWAL: 'Tajawal',
  EL_MESSIRI: 'El Messiri',
  REEM_KUFI: '<PERSON>em Kufi',
  BLAKA_HOLLOW: 'Blaka Hollow',
  MONA_SANS: 'Mona Sans',
  NOTA_KUFI: 'Noto Kufi',
};

// #. confirm types
export const CONFIRM_TYPES = {
  SKIPPED: 'skipped',
  PREVIEW: 'preview',
};

export const PAGEURLS = {
  GIFTS_DETAILS: '/group-gifts/details',
  GROUP_GIFTS: '/group-gifts',
  INVITED: '/invite-friends',
  CONTRIBUTION: `/your-contribution`,
  GREETING: `/select-greeting`,
  PERSONALIZE: `/personalize`,
  MESSAGE: `/your-message`,
  CONFIRM: `/confirm`,
  PREVIEW: '/preview',
  GIFT_OPEN: '/gift-open',
  CONTRIBUTORS_LIST: '/view-contributors',
};

export enum AMOUNT_CHANGE {
  INCREMENT = 'INCREMENT',
  DECREMENT = 'DECRIMENT',
}

export enum SOCIAL_INVITE_BASE_URL {
  MAIL = 'mailto:?subject=Group%20Gift%20invitation&body=',
  MAIL_AR = 'mailto:?subject=دعوة الهدية الجماعية&body=',
  FACEBOOK = 'https://www.facebook.com/sharer/sharer.php?u=',
  WHATSAPP = 'https://api.whatsapp.com/send?text=',
}

export const STATUS_INFO = {
  OPEN: 'OPEN',
  PROGRESS: 'PROGRESS',
  SENT: 'SENT',
  UTILIZED: 'UTILIED',
  CLOSED: 'CLOSED',
  EXPIRED: 'EXPIRED',
  DELETED: 'DELETED',
  CANCELLED: 'CANCELLED',
  CONTRIBUTE: 'CONTRIBUTE',
  INVITE: 'INVITE',
  EDIT: 'EDIT',
};
