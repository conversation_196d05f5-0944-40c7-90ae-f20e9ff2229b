import React from 'react';
import styles from './ContributionListSkelton.module.scss';
import Skeleton from '@mui/material/Skeleton';

const ContributionListSkelton = () => {
  
  const ContributionListSkeleton = Array.from({ length: 11 }, (_, index) => (
    <div key={index} className={styles['list-container']}>
      <div className={styles['list-container__left']}>
        <Skeleton variant="circular" width={30} height={30} />
        <Skeleton width={58} height={18} />
      </div>
      <div className={styles['list-container__right']}>
        <Skeleton width={58} height={18} />
      </div>
    </div>
  ));

  return (
    <div className={styles['contributionlist-skelton']}>
      <div className={styles['contributionlist-skelton__header']}>
        <Skeleton variant="circular" width={32} height={32} />
        <Skeleton width={200} height={36} className={'main-header'} />
      </div>
      <div className={styles['contributionlist-skelton__lists']}>
        <Skeleton
          variant="rectangular"
          height={50}
          className={styles['list-header']}
        />
        {ContributionListSkeleton}
      </div>
    </div>
  );
};

export default ContributionListSkelton;
