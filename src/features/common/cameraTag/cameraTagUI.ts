/*************************************************
 * NOTE: Customize third-party plug-in camera tag,
 * Exceptional case to use DOM handlers JS API's
 *************************************************/

/**
 * @method cameraTagUI
 * @description To update the Camera Tag UI by using DOM API's
 */
const cameraTagUI = ({
  t,
  uploadIconUrl,
  videoIconUrl,
  publishedIconUrl,
  saveIconUrl,
  recordAgainIconUrl,
  previewIconUrl,
}: any) => {
  /**
   * @method createStartScreenWrapper
   * @private
   */
  const createStartScreenWrapper = () => {
    const wrapperId = 'ygag-camera__start-el-wrapper';

    // #. Remove the existing wrapper, if exist
    document.getElementById(wrapperId)?.remove();

    // #. Create a new wrapper
    const wrapperEl = document.createElement('div');
    wrapperEl.id = wrapperId;

    return wrapperEl;
  };

  /**
   * @method getOrPlaceholder
   * @private
   */
  const getOrPlaceholder = () => {
    // #. Create a new wrapper
    const orEl = document.createElement('div');
    orEl.classList.add('ygag-camera__or-placeholder');
    orEl.textContent = t('or');

    return orEl;
  };

  /**
   * @method getUploadIcon
   * @private
   */
  const getVideoIcon = () => {
    const videoIcon = document.createElement('img');
    videoIcon.src = videoIconUrl;
    return videoIcon; // Return the DOM element instead of the function
  };

  /**
   * @method updateRecordElement
   * @private
   */
  const updateRecordElement = () => {
    const recordEl = document.querySelector(
      '.cameratag_primary_link.cameratag_record'
    );

    recordEl!.querySelector('.cameratag_prompt_label')!.textContent =
      t('recordFromCamera');

    // #. Remove default upload icon
    const cameraIcon = recordEl!.querySelector('i.fa');
    const recordIcon = recordEl!.querySelector('.cameratag_action_icon');
    cameraIcon!.className = 'icon-video-camera';
    recordIcon?.remove();
    // #. Get new upload icon and append it
    const newUploadicon = getVideoIcon();
    recordEl?.appendChild(newUploadicon);

    return recordEl;
  };

  /**
   * @method getUploadIcon
   * @private
   */
  const getUploadIcon = () => {
    // #. Create a new wrapper
    const uploadIcon = document.createElement('img');
    uploadIcon.src = uploadIconUrl;

    return uploadIcon;
  };

  /**
   * @method updateUploadElement
   * @private
   */
  const updateUploadElement = () => {
    const uploadEl = document.querySelector(
      '.cameratag_primary_link.cameratag_upload'
    );

    uploadEl!.querySelector('.cameratag_prompt_label')!.textContent =
      t('uploadAFile');

    // #. Remove default upload icon
    const uploadIcon = uploadEl!.querySelector('.cameratag_action_icon');
    uploadIcon?.remove();

    // #. Get new upload icon and append it
    const newUploadicon = getUploadIcon();
    uploadEl?.appendChild(newUploadicon);

    return uploadEl;
  };

  /**
   * @method customAcceptUI
   * @private
   */
  const customAcceptUI = () => {
    // #. Get accept screen elements
    const publishEl = document.querySelector('.cameratag_publish');
    const playEl = document.querySelector('.cameratag_play');
    const recordEl = document.querySelector(
      '.cameratag_rerecord_btn.cameratag_record'
    );

    // #. Override elements with custom UI
    publishEl!.innerHTML = `<img src="${saveIconUrl}"  />  ${t('upload')}`;
    playEl!.innerHTML = `<img src="${previewIconUrl}"  />  ${t('preview')}`;
    recordEl!.innerHTML = `<img src="${recordAgainIconUrl}"  />  ${t(
      'recordAgain'
    )}`;
  };
  /**
   * @method uploadingPlaceholder
   * @public
   */
  const uploadingPlaceholder = () => {
    // #. Show uploading status placeholder
    const graphEl = document.querySelector('.radial-progress');
    graphEl!.innerHTML = `<div class="uploading_progress">
<progress id="file" value="0" max="100"> 0 </progress>
<div class="bottomCont"><p>${t('uploading')}</p><p>0%</p></div></div>`;
  };

  /**
   * @method updateStartScreen
   * @public
   */
  const updateStartScreen = () => {
    // #. Get wrapper element
    const wrapperEl = createStartScreenWrapper();

    // #. Get updated child elements
    const recordEl = updateRecordElement();
    const uploadEl = updateUploadElement();

    // #. Move the children to wrapper
    Array.prototype.forEach.call([recordEl, uploadEl], function (c) {
      wrapperEl.appendChild(c);
    });

    // #. Append the or place holder
    wrapperEl.appendChild(getOrPlaceholder());

    // #. Append the wrapper
    document.getElementById('ygag-camera_start_screen')?.appendChild(wrapperEl);

    // #. Change direction style to RTL if the selected language is Arabic
    const bodyElement = document.querySelector('body');
    const dirAttributeValue = bodyElement!.getAttribute('dir');
    if (dirAttributeValue === 'rtl') {
      const cameraTagEl = document.querySelector('.camera_tag');
      cameraTagEl?.classList.add('rtl');
    }
  };

  /**
   * @method updateHardwareAccessErrorUI
   * @public
   */
  const updateHardwareAccessErrorUI = (onDialogClose: any) => {
    // #. Get wrapper element
    const errorEl = document.querySelector('.cameratag_error');

    //#. Clear all children
    errorEl!.innerHTML = '';

    // #. Get accept element
    const acceptEl = document.querySelector('.cameratag_accept');

    //#. Hide the element
    acceptEl?.classList.add('hide_element');

    // #. Create main header
    const mainHeader = document.createElement('h3');
    mainHeader.textContent = t('allowHardwareAccess');

    // #. Create error message
    const message = document.createElement('span');
    message.className = 'error-message';
    message.textContent = t('allowHardwareAccessMessage');

    // #. Create close button
    const btnClose = document.createElement('button');
    btnClose.className = 'error-close-button';
    btnClose.textContent = t('close');
    btnClose.addEventListener('click', () => {
      onDialogClose && onDialogClose();
    });

    const wrapperEl = document.createElement('div');
    wrapperEl.className = 'cameratag_error__wrapper';

    // #. Append the wrapper
    wrapperEl?.appendChild(mainHeader);
    wrapperEl?.appendChild(message);
    wrapperEl?.appendChild(btnClose);
    errorEl?.appendChild(wrapperEl);
  };

  /**
   * @method updateAcceptScreen
   * @public
   */
  const updateAcceptScreen = () => {
    // #. Show accept screen
    const acceptEl = document.querySelector('.cameratag_accept');
    acceptEl?.classList.remove('hide_element');

    // #. Customized Accept screen
    customAcceptUI();
  };

  /**
   * @method updatePublishedScreen
   * @public
   */
  const updatePublishedScreen = () => {
    // #. Get screen elements
    const wrapperDiv = document.querySelector('.cameratag_thumb_bg');
    const checkEl = document.querySelector('.cameratag_checkmark');

    // #. Override text content of the check icon element
    checkEl!.textContent = `${t('uploaded')}`;

    // #. create image element for Published Icon
    const publishedIcon = document.createElement('img');
    publishedIcon.src = publishedIconUrl;

    // #.append img element to parent
    wrapperDiv!.appendChild(publishedIcon);
  };

  /**
   * @method updatePreviewScreen
   * @public
   */
  const updatePreviewScreen = () => {
    // #. Get elements and hide accept screen
    const acceptEl = document.querySelector('.cameratag_accept');
    acceptEl?.classList.add('hide_element');
  };

  /**
   * @method updatePreviewStoppedScreen
   * @public
   */
  const updatePreviewStoppedScreen = () => {
    // #. Show accept screen and add dark overlay
    const acceptEl = document.querySelector('.cameratag_accept');
    acceptEl?.classList.remove('hide_element');
    acceptEl?.classList.add('dark_overlay');

    // #. Show customized Accept screen
    customAcceptUI();
  };

  /**
   * @method updateUploadingScreen
   * @public
   */
  const updateUploadingScreen = () => {
    // #. Remove dark overlay of the Accept screen
    const acceptEl = document.querySelector('.cameratag_accept');
    acceptEl?.classList.remove('dark_overlay');

    // #. Get accept screen elements
    const publishEl = document.querySelector('.cameratag_publish');
    const playEl = document.querySelector('.cameratag_play');
    const recordEl = document.querySelector(
      '.cameratag_rerecord_btn.cameratag_record'
    );
    // #. Remove content from the elements
    publishEl!.innerHTML = '';
    playEl!.innerHTML = '';
    recordEl!.innerHTML = '';
  };

  /**
   * @method updateCountdownScreen
   * @public
   */
  const updateCountdownScreen = () => {
    // #. Hide video accept screen
    const acceptEl = document.querySelector('.cameratag_accept');
    acceptEl?.classList.add('hide_element');
  };

  /**
   * @method updateProgressScreen
   * @public
   */
  const updateProgressScreen = (percentageVal: any) => {
    // #. Convert to percentage with two decimal places
    const percentage = (percentageVal * 100).toFixed(0);

    // #. Get progress element
    const graphEl = document.querySelector('.radial-progress');

    // #. Override the element with custom progress bar
    graphEl!.innerHTML = `<div class="uploading_progress"><progress id="file" value=${percentage} max="100"> ${percentage} </progress>
    <div class="bottomCont"><p>${t(
      'uploading'
    )}</p><p>${percentage}%</p></div></div>`;
  };

  return {
    updateStartScreen,
    updateHardwareAccessErrorUI,
    updateAcceptScreen,
    updatePublishedScreen,
    updatePreviewScreen,
    updatePreviewStoppedScreen,
    updateUploadingScreen,
    updateCountdownScreen,
    updateProgressScreen,
    uploadingPlaceholder,
  };
};

export default cameraTagUI;
