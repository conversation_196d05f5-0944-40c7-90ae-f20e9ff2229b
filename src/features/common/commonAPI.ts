import { SITE_CONFIG_QUERY, STORES_QUERY } from './common.query';
import { PLATFORM_TYPE } from '@/constants/common';
import { useQuery } from '@apollo/client';
import { getClient } from '@/graphql/apolloClient';
/**
 * @method getStoresData
 * @description Get the list of stores and details
 * @returns storesDataLoading, storesDataError,storesData,
 */
export const getStoresData = () => {
  const {
    loading: storesDataLoading,
    error: storesDataError,
    data: storesData,
  } = useQuery(STORES_QUERY, {
    variables: {
      platformType_Code: PLATFORM_TYPE.WEB,
    },
    context: {
      clientName: 'webstore-with-cdn',
    },
  });

  return {
    storesDataLoading,
    storesDataError,
    storesData,
  };
};

/**
 * @method getSiteConfig
 * Check1: returns locale info from the query slug
 * Check2: If query missed, then take it from the site config
 */
export const getSiteConfig = async (
  ip: any,
  locale: string,
  locales: Array<string> = []
) => {
  let languageCode = 'en';
  let regionCode = '';

  return getClient(locale)
    .query({
      query: SITE_CONFIG_QUERY,
      variables: {
        platformType_Code: PLATFORM_TYPE.WEB,
        store: regionCode ? regionCode.toUpperCase() : '',
      },
      context: {
        headers: {
          'Yg-Ip-Address': ip,
        },
        clientName: 'webstore',
      },
    })
    .then((result) => {
      const networkStatus = result.networkStatus;
      const data = result?.data?.siteConfigs?.edges;
      languageCode = data[0]?.node?.defaultLanguage;
      regionCode = data[0]?.node?.defaultCountry;

      const results = [
        languageCode?.toLowerCase(),
        regionCode?.toLowerCase(),
        networkStatus,
      ];

      return results;
    })
    .catch((error) => {
      console.error(error);
      if (error?.graphQLErrors) {
        return [{ error: error?.graphQLErrors[0] }];
      }
      return [{ error }];
    });
};
