'use client';
import Link from 'next/link';
import React, { useEffect, useState } from 'react';
import Loader from '../loader/Loader';

const CustomLink = ({
  hrefLink,
  aClass,
  aId,
  children,
  aClick,
  aTitle = '',
  aTestId = '',
  aTarget = '',
  passHref = true,
  prefetch = true,
  key = '',
  replace = false,
  shallow = false,
  rel = '',
  disableCTA = false,
}: any) => {
  const [showLoader, setShowLoader] = useState(false);

  const handleClick = (e: any) => {
    if (!disableCTA) {
      setShowLoader(true);
      aClick?.(e);
    }else{
       e.preventDefault();
    }
  };
  return (
    <React.Fragment>
      <Link
        key={key}
        prefetch={prefetch}
        passHref={passHref}
        legacyBehavior
        href={hrefLink}
        replace={replace}
        shallow={shallow}
        rel={rel}
      >
        <a
          className={aClass}
          onClick={handleClick}
          id={aId}
          title={aTitle}
          data-testid={aTestId}
          target={aTarget}
        >
          {children}
        </a>
      </Link>
      {showLoader && <Loader />}
    </ React.Fragment>
  );
};

export default CustomLink;
