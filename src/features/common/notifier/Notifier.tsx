import { useEffect, useState } from 'react';
import styles from './Notifier.module.scss';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import useCommonSlice from '../commonSlice';
import SvgIcon from '../svgIcon/SvgIcon';

const icons: any = {
  successIcon: <SvgIcon icon="success-icon" width="24" height="24"/>,
  failerIcon: <SvgIcon icon="failer-icon" width="24" height="24" />,
  infoIcon: <SvgIcon icon="info-icon" width="24" height="24" />,
};

/**
 * @component Notifier
 * @description Common component to display the notifications
 * @param param0
 * @returns
 */
const Notifier = () => {
  // #. Notifier state
  const [showNotifier, setShowNotifier] = useState<boolean>(false);
  const { getNotifierState, setNotifierState } = useCommonSlice();

  // #. Get notification details from the state
  const notifierState = useAppSelector(getNotifierState);

  const {
    title,
    description,
    icon,
    autoHideDuration = 5000,
    onClose,
  }: any = notifierState || {};
  // #. Dipatch the hidden state
  const dipatch = useAppDispatch();

  useEffect(() => {
    // #. Must have title to show the notification
    if (title) {
      setShowNotifier(true);
      // #. Autohide the notifier
      setTimeout(() => {
        setShowNotifier(false);
        onClose && onClose();
        // #. Dispath the hidden state - title muse be false
        dipatch(
          setNotifierState({
            title: '',
            description: '',
          })
        );
      }, autoHideDuration);
    }

    return () => {
      setShowNotifier(false);
    };
  }, [title]);

  return (
    <>
      {showNotifier && (
        <div className={`${styles['notifier']} notifier`}>
          <div
            className={`${styles['notifier__container']}`}
          >
            <div className={` ${styles['notifier__icon']} 
            ${icon === 'failerIcon' ? styles["notifier__icon-failer"] : ''}
            ${icon === 'infoIcon' ? styles["notifier__icon-info"] : ''}
            `} data-testid="notiIcon">
              {icons[icon]}
            </div>
            <div className={styles['notifier__text-section']}>
              <div
                className={`${styles['notifier__title']}`}
                data-testid="notiTitle"
              >
                {title}
              </div>
              {description && (<div
                className={`${styles['notifier__description']} ${
                  icon === 'successThumb'
                    ? styles['notifier__success-description']
                    : ''
                }`}
                data-testid="notiDescription"
              >
                {description}
              </div>)}
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default Notifier;
