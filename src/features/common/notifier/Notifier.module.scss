@import '@/styles/abstracts/abstracts';

.notifier {
  position: fixed;
  top: 24px;
  left: 50%;
  z-index: 9999;
  border-radius: 6px;
  color: #000;

  &__container {
    min-height: 56px;
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
    background-color: $dark-charcoal;  
    position: relative;
    left: -50%;
    border-radius: 12px;
    padding: 12px 16px;
    display: flex;
    gap: 12px;
    align-items: center;
    box-shadow: 0px 16px 20px -8px rgba(3, 5, 18, 0.10);
  }

  &__success-container {
    gap: 10px;
    box-shadow: 0 0 20px 0 rgba(161, 187, 201, 0.25);
    padding: 15px;
    height: 64px;
    min-width: 420px;
  }

  &__icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background-color: rgba(101, 207, 33, 0.10);
    border-radius: 40px;

    &-failer{
      background-color: rgba(231, 72, 72, 0.10);
    }

    &-info{
      background-color: rgba(255, 255, 255, 0.10);
    }
  }

  &__text-section {
    display: flex;
    flex-direction: column;
    gap: 7px;
  }

  &__title {
    font-size: rem(14);
    font-weight: 600;
    color: $white;
    line-height: 18px;
    letter-spacing: -0.14px;
    min-width: 274px;
  }

  &__description {
    font-size: rem(14);
    color: #3a1a5f;
    font-weight: 400;
  }

  &__success-description {
    color: #545454;
  }
}
