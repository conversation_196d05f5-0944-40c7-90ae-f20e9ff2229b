import { gql } from '@apollo/client';

export const FOOTER_QUERY = gql`
  query FooterQuery($platformType_Code: String!, $country_Code: String!) {
    footer(platformType_Code: $platformType_Code, country_Code: $country_Code) {
      logo
      tagLine
      copyrightNotice
      fontSize
      footerStores {
        storeLink
        store {
          country {
            name
          }
        }
      }
      subMenu {
        itemLabel
        itemCode
        itemAlignment
        children {
          edges {
            node {
              itemCode
              itemName
              itemLabel
              itemCaption
              itemIcon
              itemUrl
              itemImage
              itemType
              isPopup
              id
              itemIconWebp
              itemImageWebp
            }
          }
        }
      }
      appBanner {
        itemUrl
        itemImage
        itemName
      }
      socialMediaIcon {
        itemLabel
        itemUrl
        itemImage
      }
    }
  }
`;

export const HEADER_QUERY = gql`
  query HeaderQuery($platformType_Code: String!) {
    headers(platformType_Code: $platformType_Code) {
      edges {
        node {
          tagLine
          logo
          eGiftCards_SeoName
          gamingCards_SeoName
          headerType {
            code
          }
        }
      }
    }
  }
`;

export const STORES_QUERY = gql`
  query StoresQuery {
    stores {
      edges {
        node {
          code
          name
          timezone
          country {
            currency {
              code
            }
            name
            code
            name
            flagImage
            codeThreeLetter
            circularStoreLogo
            defaultLanguage {
              code
            }
          }
          languages {
            edges {
              node {
                name
                code
              }
            }
          }
        }
      }
    }
  }
`;

export const DOWNLOAD_APP_QUERY = gql`
  query downloadApp {
    downloadApp {
      bannerImage
      appBanner {
        itemName
        itemUrl
        itemImage
        id
      }
      roundedAppBanner{
      id,
      itemName
      itemImage
      itemUrl
      }
      appDownloadQrcode
    }
  }
`;

export const PAYMENT_PARTNERS_QUERY = gql`
  query paymentPartners($platformType_Code: String!, $country_Code: String!) {
    footer(platformType_Code: $platformType_Code, country_Code: $country_Code) {
      paymentPartners {
        edges {
          node {
            code
            logo
          }
        }
      }
    }
  }
`;

export const SITE_CONFIG_QUERY = gql`
  query SiteConfigQuery($platformType_Code: String!, $store: String!) {
    siteConfigs(platformType_Code: $platformType_Code, store: $store) {
      edges {
        node {
          defaultCountry
          defaultLanguage
          defaultStoreCode
          languages
          atworkEnabled
          solutionHubEnabled
        }
      }
    }
  }
`;

export const AUTH_TOKENS_QUERY = gql`
  query AuthTokens($input: AuthGetTokensInput) {
    authTokens(input: $input) {
      accessToken
      idToken
      refreshToken
    }
  }
`;
