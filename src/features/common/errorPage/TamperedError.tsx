import { useTranslation } from 'react-i18next';
import styles from './TamperedError.module.scss';
import { imageBaseUrl } from '@/constants/envVariables';

const TamperedError = () => {
  const { t } = useTranslation();

  // taking public image config url
  const errorIcon = `${imageBaseUrl}/images/icons/error-icon.svg`;

  return (
    <div className={styles['tampered-error']}>
      <h4>{t('guestTitle')}</h4>
      <p>{t('guestContents2')}</p>
      <img src={errorIcon} alt="error-icon"/>
      <p>{t('guestContents')}</p>
    </div>
  );
};

export default TamperedError;
