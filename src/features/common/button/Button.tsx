import React, { useEffect, useState } from 'react';

import styles from './Button.module.scss';
import SvgIcon from '../svgIcon/SvgIcon';
import CircularProgress from '@mui/material/CircularProgress';

/**
 * @method Button
 * @description Button component
 * @returns {JSX.Element}
 */

const Button = ({
  theme,
  className,
  action,
  children,
  borderTheme,
  arrow,
  id,
  attribues = {},
  icon,
  loader,
  wrapperClassName
}: any): JSX.Element => {
  const [isMounted, setIsMounted] = useState(false);

  // Set the mounted state to true after the component has mounted
  useEffect(() => {
    const timeout = setTimeout(() => {
      setIsMounted(true);
    }, 0);

    return () => clearTimeout(timeout);
  }, []);

  const buttonClasses = [
    styles.button,
    styles[`button--${theme}`],
    borderTheme && styles.border,
    borderTheme && styles[`border--${borderTheme}`],
    arrow && styles["arrow-enabled"],
    className,
    isMounted ? styles["hover-enabled"] : "" // Add hover-enabled class after mount
  ].filter(Boolean).join(" ");

  const wrapperClasses = [
    styles["btn-wrapper"],
    theme === "primary" && !attribues?.disabled ? styles["btn-wrapper__bg"] : "",
    wrapperClassName
  ].filter(Boolean).join(" ");
  
  return (
    <div className={wrapperClasses}>
    <button
      className={buttonClasses}
      data-testid="themedButton"
      onClick={action}
      id={id}
      {...attribues}
    >
      {icon && (
        <span className={styles['button-icon']}>
          <SvgIcon icon={icon} width="20" height="20" />
        </span>
      )}
      {children && !loader && (
        <span className={`${styles['button-label']} button-label`}>
          {children}
        </span>
      )}
      {arrow === 'arrow-forward' && !loader &&(
        <span className={` ${styles['icon-wrapper']} button-icon-wrapper`}>
          <SvgIcon icon="arrow" width="24" height="24" />
        </span>
      )}
      {loader && (
        <span className={` ${styles['button-spinner']} button-spinner-wrapper`}>
           <CircularProgress  /> 
        </span>
      )}
    </button>
    </div>
  );
};

export default Button;
