@import '@/styles/abstracts/abstracts';

%icon-wrapper {
  width: 40px;
  height: 40px;
  display: inline-block;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: transform ease-in-out 400ms;
}

.btn-wrapper{
  border-radius: 8px;
  flex: 1;

  &__bg{
    background: linear-gradient(90deg, #FF65C1, #8168FF, #FF7A7A, #2082FF);
  }
}

.button {
  padding: 4px;
  cursor: pointer;
  border: 0;
  border-radius: 8px;
  width: 100%;
  transform: translate(0, 0);

  &:disabled {
    box-shadow: none;
    pointer-events: none;
    opacity: 0.5;
    height: 50px;
  }

  &--primary {
    background: $dark-charcoal;
    color: $white;
    animation: returnToOriginal 0.3s ease forwards;
    height: 50px;
    font-size: rem(16);
    font-weight: 700;
    line-height: 24px;
    letter-spacing: -0.16px;
    @include rtl-styles{
      animation: returnToOriginalRtl 0.3s ease forwards;
    }

    &.hover-enabled {
    &:hover{
      animation: bounceUp 0.2s ease forwards;

      @include rtl-styles{
        animation: bounceUpRtl 0.2s ease forwards;
      }
    }
    }
  }
  &--secondary{
    background: $white;
    color: $dark-charcoal;
    border: 2px solid $dark-charcoal;
    transition: background 0.5s ease, color 0.5s ease;
    &.hover-enabled {
      &:hover{
      background: $dark-charcoal;
      color: $white;
    }
    }

    span svg {
      color: $black;
    }
  }

  &--small-grey{
    color: $dark-charcoal;
    background-color: $medium-light-grey;
    border-radius: 24px;
    padding: 0px 16px;
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
    height: 32px;
  }

  &--purple {
    background: $barney-purple;
    color: $white;
  }

  &--radical-red {
    background: $radical-red;
    color: $white;
  }

  &--pale-grey {
    background: $medium-light-grey !important;
    color: $dark-charcoal !important;
  }

  &--pale-grey {
    background: $pale-grey;
    color: $title-color;
  }

  &--error {
    background-color: $mild-red;
    color: $white;
  }

  &--gift,
  &--invite {
    // padding: 16px;
    width: 100%;
    font-size: 16px;
    height: 50px;
    color: $barney-purple;
    font-weight: 500;
  }

  &--capsule-button {
    background-color: $pale-grey;
    padding: rem(8) rem(15);
    font-size: rem(14);
    font-weight: 500;
    color: $title-color;
    border-radius: rem(24);
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  &--continue {
    color: $off-white;
    background-color: $barney-purple;
    width: 100%;
    height: 50px;
    font-size: rem(16);
    padding: rem(16);
    font-weight: 500;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  &--transparent {
    color: $barney-purple;
    background-color: transparent;
    width: 100%;
    font-size: rem(16);
    padding: rem(16);
    cursor: default;
    span {
      cursor: pointer;
    }
  }

  .button-icon {
    margin-right: 6px;
  }

  .button-spinner{
    width: 100%;
  }

  .icon-wrapper {
    transition: all 0.4s ease;
    display: flex;
    align-items: center;
  }

  &:hover {
    .icon-wrapper {
      transform: translateX(4px);
      transition: all 0.4s ease;
    }
  }

  .large {
    padding: 0;
  }

  .icon-wrapper {
    transition: all 0.4s ease;
    display: flex;
    align-items: center;

    @include rtl-styles {
      // transform: scaleX(-1);
      rotate: 180deg;
      margin-top: 0;
    }
  }
  &:hover {
    .icon-wrapper {
      transform: translateX(4px);
      transition: all 0.4s ease;
    }
  }
}

.arrow-enabled {
  display: flex;
  align-items: center;

  .button-label {
    padding: 5px 25px 5px 40px;
    display: inline-block;

    @include rtl-styles {
      padding: 5px 40px 5px 25px;
    }
  }
}

.border {
  border: 1px solid transparent;

  &--purple {
    border-color: $barney-purple;
  }
}

.white {
  &-icon-wrapper {
    @extend %icon-wrapper;

    background-color: rgba($cornflower-blue, 10%);
  }
}

.button-icon-wrapper {
  display: flex;
}

// Define animations for each direction
@keyframes bounceUp {
  0% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(1px, -1px);
  }
  50% {
    transform: translate(2px, -2px);
  }
  75% {
    transform: translate(3px, -3px);
  }
  100% {
    transform: translate(4px, -4px);
  }
}

@keyframes returnToOriginal {
  0% {
    transform: translate(4px, -4px);
  }
  100% {
    transform: translate(0, 0);
  }
}

@keyframes bounceUpRtl {
  0% {
    transform: translate(0, 0);
  }
  25% {
    transform: translate(-1px, -1px);
  }
  50% {
    transform: translate(-2px, -2px);
  }
  75% {
    transform: translate(-3px, -3px);
  }
  100% {
    transform: translate(-4px, -4px);
  }
}

@keyframes returnToOriginalRtl {
  0% {
    transform: translate(-4px, -4px);
  }
  100% {
    transform: translate(0, 0);
  }
}
