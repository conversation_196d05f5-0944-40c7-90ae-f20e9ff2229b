'use client';

import { imageBaseUrl } from '@/constants/envVariables';
import { useTranslation } from 'react-i18next';

const CookieDisabled = () => {
  // #. Set current year
  const date = new Date();
  let year = date.getFullYear();
  const { t } = useTranslation();

  // #. Getting Image
  const ygagLogo = `${imageBaseUrl}/images/ygag-logo.png`;

  return (
    <div>
      <div className="back_Soon">
        <header>
          <div className={'back_Soon_header'}>
            <a href={`/`}>
              <img src={ygagLogo} width={73} height={50} alt="YOUGotaGift" />
            </a>
          </div>
        </header>

        <section className={`container ${'back_Soon_main-section'}`}>
          <div>
            {/* <h2>Uh Oh!</h2> */}
            <h5>{t('cookieDesc')}</h5>
            <div>
              <img
                src={`${imageBaseUrl}/images/icons/error.svg`}
                width={135}
                height={164}
                alt="error"
              />
            </div>
          </div>
        </section>

        <footer className={'back_Soon_footer'}>
          <div className={`container ${'container'}`}>
            <div>
              <p>©{year} YOUGotaGift.com Ltd. All rights reserved.</p>
            </div>
            <div>
              <ul>
                <li>
                  Available in UAE <span>|</span> Saudi Arabia <span>|</span>{' '}
                  Oman <span>|</span> Bahrain <span>|</span> Qatar{' '}
                  <span>|</span> Lebanon <span>|</span> India <span>|</span>{' '}
                  Africa <span>|</span> Europe &amp; USA.
                </li>
              </ul>
            </div>
          </div>
        </footer>
      </div>
    </div>
  );
};

export default CookieDisabled;
