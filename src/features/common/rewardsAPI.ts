import { useMutation, useQuery } from '@apollo/client';
import { DATA_POINT_HIT, REWARDS_AUTH } from './rewards.mutation';
import { PLATFORM_TYPE } from '@/constants/common';
import { REWARDS_CORPORATE_ID, REWARDS_COUNTRIES } from '../startGroupGift/startGroupGift.query';

const useRewardsAPI = () => {
  // #. rewards auth mutation query
  const [rewardsAuth] = useMutation(REWARDS_AUTH);

  // #. rewards data point api
  const [dataPointHit] = useMutation(DATA_POINT_HIT);

  /**
   * @method groupGiftRewardsAuth
   * @description Groupgift rewards authentication
   * @returns accessToken
   */
  const groupGiftRewardsAuth = async () => {
    try {
      const response = await rewardsAuth({
        context: {
          credentials: 'include',
        },
      });
      return response;
    } catch (error) {
      console.error(error);
      return error;
    }
  };

  /**
   * @method rewardsDataPoint
   * @description Groupgift rewards datapoint hit api
   */
  const rewardsDataPoint = async (input: object, AccessToken: String) => {
    try {
      const response = await dataPointHit({
        context: {
          headers: {
            'App-Platform': PLATFORM_TYPE.REWARDS,
            Authorization: `JWT ${AccessToken}`,
          },
        },
        variables: {
          input,
        },
      });
      return response;
    } catch (error) {
      console.error(error);
      return error;
    }
  };

  /**
   * @method getRewardsStores
   * @description Get the list of stores from rewards
   * @returns rewardsCountriesLoading, rewardsCountries
   */
  const getRewardsStores = (
    locale: string = 'en',
    isRewards: boolean = false
  ) => {
    const { data: rewardsCountries, loading: rewardsCountriesLoading } =
      useQuery<any>(REWARDS_COUNTRIES, {
        context: {
          headers: {
            'App-Platform': PLATFORM_TYPE.REWARDS,
            'Accept-Language': locale,
          },
        },
        skip: !isRewards,
      });

    return {
      rewardsCountries,
      rewardsCountriesLoading,
    };
  };

  /**
   * @method getGGTrackableLink
   * @description Get the rewards corporate id
   * @returns
   */
  const getGGTrackableLink = (locale: string = 'en', AccessToken: String) => {
    const { data: rewardsTrackableLink, loading: rewardsTrackableLinkLoading } =
      useQuery<any>(REWARDS_CORPORATE_ID, {
        context: {
          headers: {
            'App-Platform': PLATFORM_TYPE.REWARDS,
            'Accept-Language': locale,
            Authorization: `JWT ${AccessToken}`,
          },
        },
      });
    return { rewardsTrackableLink, rewardsTrackableLinkLoading };
  };

  return {
    groupGiftRewardsAuth,
    rewardsDataPoint,
    getRewardsStores,
    getGGTrackableLink,
  };
};

export default useRewardsAPI;
