@import '@/styles/abstracts/abstracts';

.country-phone-code {
  display: flex;
  &-container {
    display: flex;
    @include rtl-styles {
      display: flex;
      gap: 5px;
      align-items: center;

      > span + span {
        unicode-bidi: embed;
        direction: ltr;
      }
    }
    > span {
      img {
        width: 18px;
        height: 18px;
        margin-top: 3px;
      }
    }
  }
  .country-select {
    width: 100% !important;
    color: $dark-purple !important;
  }

  &__menu-item {
    padding: 7px 16px 7px 16px !important;
    display: grid !important;
    grid-template-columns: 24px 1fr 50px;
    gap: 10px;
    min-height: 23px !important;
    font-size: 14px !important;

    &:hover {
      background-color: $light-purple !important;
      border-radius: 6px;
    }

    // > div {
    //   width: 16px;
    //   height: 16px;
    //   margin: 0 16px 4.4px 0;

    //   > span {
    //     font-size: rem(14)
    //   }
    // }

    > span {
      display: flex;
      color: $dark-purple !important;
      white-space: break-spaces;
    }
  }

  &__menu-item-send_to {
    padding: 7px;
    display: grid;
    grid-template-columns: 70px auto;
    gap: 10px;
    min-height: 23px;
    font-size: 14px;

    &:hover {
      background-color: $light-purple;
      border-radius: 6px;
    }

    > div {
      > img {
        width: 30px;
        height: 30px;
        object-fit: contain;
        border-radius: 3px;
      }

      > span {
        font-size: rem(20);
      }
    }
  }

  &__search-box {
    // top: -20px !important;
    // padding: 10px 0 0;

    // div {
    //   background-color: #e8ecf0;
    // }
    padding: 16px 16px 8px 16px;
  }

  &__search-input {
    div {
      padding-left: 10px !important;

      @include rtl-styles {
        padding-right: 10px !important;
      }
    }

    input {
      padding: 0;
      padding-left: 11px !important;
      height: 50px;
      min-height: 23px;
      // opacity: 0.4;
      // border-radius: 6px;
      // background-color: $border-color-sign-input; //need to verify
      // font-size: rem(16);

      border-radius: 8px;
      @include font-size(14);

      @include rtl-styles {
        padding-right: 11px !important;
      }

      &::placeholder {
        font-size: rem(14);

        width: 94px;
        height: 20px;
        margin: 0 0 0 14px;
        font-family: Poppins !important;
        font-size: 14px;
        opacity: 1 !important;
        font-weight: 500;
        font-stretch: normal;
        font-style: normal;
        line-height: 2.86;
        letter-spacing: normal;
        text-align: left;
        color: $placeholder-gray;
      }
    }
  }

  &__search-icon {
    width: 24px;
    height: 24px;
  }

  &__flag {
    width: 24px;
    height: 24px;

    > span {
      @include font-size(14);
    }

    img {
      max-height: 24px;
    }
  }

  &__code {
    text-align: right;
  }
}
