import React, { useEffect, useRef, useState } from 'react';
import styles from './CountryPhoneCode.module.scss';
import {
  getCountries,
  getCountryCallingCode,
} from 'react-phone-number-input/input';
import { ListSubheader, MenuItem, Select, TextField } from '@mui/material';
import getUnicodeFlagIcon from 'country-flag-icons/unicode';
import en from 'react-phone-number-input/locale/en.json';
import ar from 'react-phone-number-input/locale/ar.json';
import isAlphabet from '@/utils/isAlphabet';
import { useTranslation } from 'next-i18next';
import { styled } from '@mui/material/styles';
import { outlinedInputClasses } from '@mui/material/OutlinedInput';
import { CountryCode } from 'libphonenumber-js';
import { imageBaseUrl } from '@/constants/envVariables';
import twemoji from 'twemoji';
import { JS_DELIVR_CDN } from '@/constants/common';
import SvgIcon from '../svgIcon/SvgIcon';

const StyledTextField = styled(TextField)({
  [`& .${outlinedInputClasses.root} .${outlinedInputClasses.notchedOutline}`]: {
    borderWidth: '1px !important',
    borderColor: "#d9d9d9 !important",
    borderRadius: "8px"
  },
  // [`&:hover .${outlinedInputClasses.root} .${outlinedInputClasses.notchedOutline}`]:
  //   {
  //     borderWidth: '1px !important',
  //     borderColor: 'rgba(0, 0, 0, 0.23) !important',
  //   },
});


const CountryPhoneCode = ({
  phoneNumberRef,
  showCountryName = true,
  defaultValue,
  setCountryCallback,
  showCountryNameInSelection = true,
  dialCode,
  enableSearch = false,
  language = 'en',
}: {
  phoneNumberRef: React.RefObject<HTMLInputElement>;
  showCountryName: boolean;
  defaultValue: CountryCode;
  setCountryCallback: any;
  showCountryNameInSelection: boolean;
  dialCode?: string;
  enableSearch?: boolean;
  language?: string;
}) => {
  const { t } = useTranslation('common');

  // # Set the reference of the search input
  const searchRef = useRef<HTMLInputElement>(null);

  const countriesList = getCountries();
  const [countryCode, setCountryCode] = useState<CountryCode>(defaultValue);
  const [searchText, setSearchText] = useState('');
  const [countryOptions, setCountryOptions] = useState<any>(countriesList);
  const defaultLan = language == 'ar' ? ar : en;

  const handleSelectOpen = () => {
    setTimeout(() => {
      searchRef.current?.focus();
    }, 100);
  };

  const onChangeHandler = async (event: any) => {
    if (phoneNumberRef.current) phoneNumberRef.current.value = '';
    const countryCode = event?.target?.value;
    const dialCode = `+${getCountryCallingCode(countryCode)}`;

    if (!countryCode) return;
    setCountryCode(countryCode);
    setCountryCallback({ countryCode, dialCode });
    setCountryOptions(countriesList);
  };

  const containsText = (text: string, searchText: string) =>
    text.toLowerCase().indexOf(searchText.toLowerCase()) > -1;

  useEffect(() => {
    // #. Set country code based on the dial-code
    if (dialCode) {
      for (let i = 0; i < countriesList.length; i++) {
        const countryDialCode = `+${getCountryCallingCode(countriesList[i])}`;
        if (dialCode === countryDialCode) {
          setCountryCode(countriesList[i]);
          break;
        }
      }
    }
  }, [dialCode]);

  useEffect(() => {
    let filteredCountries = Object.keys(en)?.filter((key: any) => {
      if (!['ext', 'country', 'phone', 'ZZ', 'AB'].includes(key)) {
        let text = en[key as keyof typeof en];
        if (containsText(text, searchText)) {
          return defaultLan[key as keyof typeof defaultLan];
        }
      }
    });
    setCountryOptions(filteredCountries);
    setTimeout(() => {
      searchRef.current?.focus();
    }, 100);
  }, [searchText]);

  const getCountryCode = (country: any) => {
    const isValidCountry = getCountries().includes(country?.toUpperCase());
    return isValidCountry ? getCountryCallingCode(country) : "0";
  };

  return (
    <div className={`country-phone-code ${styles['country-phone-code']}`}>
      <Select
        value={countryCode}
        renderValue={(id) => {
          return (
            <div className={styles['country-phone-code-container']}>
              <span  
                className={styles['country-phone-code-flag']}
                dangerouslySetInnerHTML={{
                __html: twemoji.parse(getUnicodeFlagIcon(id), {
                  base: JS_DELIVR_CDN,
                  folder: 'svg',
                  ext: '.svg'
                })
              }}/>
              {showCountryNameInSelection && (
                <span>&nbsp;&nbsp;{defaultLan[id]}</span>
              )}
              <span>&nbsp;+{getCountryCode(id)}</span>
            </div>
          );
        }}
        MenuProps={{
          transformOrigin: {
            vertical: "top", 
            horizontal: "left",
          },
          classes: {
            paper: `paper-signin-dropdowns country-phone-code`,
          },
        }}
        className={styles['country-select']}
        onChange={onChangeHandler}
        onClick={() => {
          if (searchText && searchText.length == 0) {
            setCountryOptions(setCountryOptions(countriesList));
          }
        }}
        onOpen={handleSelectOpen}
        IconComponent={()=>(
          <SvgIcon icon="arrow-down-country-code" width="16" height="16" />
        )}
      >
        {enableSearch && (
          <ListSubheader className={styles['country-phone-code__search-box']}>
            <StyledTextField
              size="small"
              // Autofocus on textfield
              inputRef={searchRef}
              placeholder={t('searchHere')}
              fullWidth
              InputProps={{
                startAdornment: (
                  <img
                    className={styles['country-phone-code__search-icon']}
                    src={`${imageBaseUrl}/images/feather-search.png`}
                    alt="Search"
                  />
                ),
              }}
              className={styles['country-phone-code__search-input']}
              onChange={(e: any) => setSearchText(e.target.value)}
              onKeyDown={(e) => {
                if (e.key !== 'Escape') {
                  // Prevents autoselecting item while typing (default Select behaviour)
                  e.stopPropagation();
                }
              }}
              onKeyPress={(e: any) => {
                if (!isAlphabet(e)) {
                  e.preventDefault();
                }
              }}
            />
          </ListSubheader>
        )}

        {countryOptions?.map((country: any) => (
          <MenuItem
            key={country}
            value={country}
            className={`${
              showCountryName === true
                  ? styles['country-phone-code__menu-item']
                  : styles[
                        'country-phone-code__menu-item-send_to'
                    ]
          }`}
          >
            {
              <span  
                className={styles['country-flag']}
                dangerouslySetInnerHTML={{
                __html: twemoji.parse(getUnicodeFlagIcon(country), {
                  base: JS_DELIVR_CDN,
                  folder: 'svg',
                  ext: '.svg'
                })
                }}
              />
            }
            {showCountryName && (
              <span>{defaultLan[country as keyof typeof defaultLan]}</span>
            )}
            {
              <div className={styles["country-phone-code__code"]}>
                {`+${getCountryCode(country)}`}
              </div>
            }
          </MenuItem>
        ))}
      </Select>
    </div>
  );
};

CountryPhoneCode.defaultProps = {
  showCountryName: true,
  defaultValue: 'AE',
  showCountryNameInSelection: true,
  enableSearch: false,
  language: 'en',
  phoneNumberRef: null,
};

export default CountryPhoneCode;
