import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import type { AppState } from '@/redux/store';

interface NotifierInterface {
  title: string;
  description: string | unknown;
  icon?: string;
  autoHideDuration?: number;
  onClose?: () => void;
}

export interface CommonSlice {
  locale: string | undefined;
  isLoading: boolean;
  notifier: NotifierInterface;
  tokenInfo:  object | any;
  selectedStore: object;
  stores: object;
  rewardsStores: object;
  guestInfo: object;
  isDownloadAppVisible: boolean;
}

const initialState: CommonSlice = {
  locale: '',
  isLoading: false,
  notifier: {
    title: '',
    description: '',
  },
  tokenInfo: {},
  guestInfo: {},
  rewardsStores: [],
  selectedStore: [],
  stores: [],
  isDownloadAppVisible: false
};

// #. Common slice contains the common state methods
export const commonSlice = createSlice({
  name: 'common',
  initialState,
  reducers: {
    setLocale: (state, action: PayloadAction<string>) => {
      state.locale = action.payload;
    },
    showLoader: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setNotifierState: (state, action: PayloadAction<NotifierInterface>) => {
      state.notifier = action.payload;
    },
    setTokenInfo: (state, action: PayloadAction<any>) => {
      state.tokenInfo = action.payload;
    },
    setGuestInfo: (state, action: PayloadAction<any>) => {
      state.guestInfo = action.payload;
    },
    setSelectedStore: (state, action: PayloadAction<any>) => {
      state.selectedStore = action.payload;
    },
    setStores : (state, action: PayloadAction<any>) => {
      state.stores = action.payload;
    },
    setRewardsStores: (state, action: PayloadAction<any>) => {
      state.rewardsStores = action.payload;
    },
    setIsDownloadAppVisible: (state, action: PayloadAction<any>) => {
      state.isDownloadAppVisible = action.payload;
    },
  },
});

// #. Get all reducers
const commonSliceReducer = commonSlice.reducer;

/**
 * Custom hook to get the common states
 * @returns
 */
function useCommonSlice(): any {
  const {
    setLocale,
    showLoader,
    setTokenInfo,
    setSelectedStore,
    setNotifierState,
    setStores,
    setGuestInfo,
    setRewardsStores,
    setIsDownloadAppVisible
  } = commonSlice.actions;

  // #. Get actions
  const selectIsLoading = (state: AppState) => state.common.isLoading;
  const getTokenInfo = (state: AppState) => state.common.tokenInfo;
  const getGuestInfo = (state: AppState) => state.common.guestInfo;
  const getSelectedStore = (state: AppState) => state.common.selectedStore;
  const getNotifierState = (state: AppState) => state.common.notifier;
  const getStores = (state: AppState) => state.common.stores;
  const getRewardsStores = (state: AppState) => state.common.rewardsStores;
  const getIsDownloadAppVisible = (state: AppState) => state.common.isDownloadAppVisible;

  return {
    showLoader,
    selectIsLoading,
    setLocale,
    setNotifierState,
    getNotifierState,
    setTokenInfo,
    setGuestInfo,
    getTokenInfo,
    getGuestInfo,
    setSelectedStore,
    getSelectedStore,
    setStores,
    getStores,
    setRewardsStores,
    getRewardsStores,
    getIsDownloadAppVisible,
    setIsDownloadAppVisible
  };
}

export { commonSliceReducer };

export default useCommonSlice;
