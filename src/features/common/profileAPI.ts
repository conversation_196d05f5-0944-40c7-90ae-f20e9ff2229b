import { useMutation, useQuery } from '@apollo/client';
import { USER_DETAILS_QUERY } from './profile.query';
import { UserDetailsDataInterface } from '@/interfaces/common.inteface';
import { AUTH_REVOKE_TOKENS } from './users.mutation';

const useProfileAPI = () => {
  // #. Set the apollo mutation object method
  const [authRevokeTokens] = useMutation(AUTH_REVOKE_TOKENS);

  /**
   * @method revokeTokens
   * @param {string} refereshToken
   * @returns {object} data
   */
  const RevokeTokens = async (token: any) => {
    try {
      const { data } = await authRevokeTokens({
        context: {
          clientName: 'ecom-users',
          headers: {
            'refresh-token': token,
          },

          credentials: 'include',
        },
      });
      return data;
    } catch (error) {
      console.warn(error, 'error');
      return error;
    }
  };

  /**
   * @method GetUserDetails
   * @description Get user details on client side
   * @param accessToken
   * @returns userDetailsLoading, userDetailsError, userDetailsData, refetchUserDetails,
   */
  const GetUserDetails = (accessToken: string = '', locale: string = 'en') => {
    const input = {
      accessToken: accessToken,
    };

    const {
      loading: userDetailsLoading,
      error: userDetailsError,
      data: userDetailsData,
      refetch: refetchUserDetails,
    } = useQuery<UserDetailsDataInterface>(USER_DETAILS_QUERY, {
      variables: {
        input,
      },
      context: {
        clientName: 'ecom-users',
        headers: {
          'accept-language': locale,
        },
      },
      skip: !Boolean(accessToken),
    });

    return {
      userDetailsLoading,
      userDetailsError,
      userDetailsData,
      refetchUserDetails,
    };
  };

  return {
    GetUserDetails,
    RevokeTokens,
  };
};

export default useProfileAPI;
