'use client';
import React, { useEffect, useState } from 'react';
import styles from './MajorHeader.module.scss';
import DropDownMenu from '../../dropDown/DropDownMenu';
import Link from 'next/link';
import SvgIcon from '../../svgIcon/SvgIcon';
import { imageBaseUrl } from '@/constants/envVariables';
import getRegionAndLocale from '@/utils/getRegionAndLocale';
import { useTranslation } from 'react-i18next';
import useCommonSlice from '../../commonSlice';
import { useAppDispatch } from '@/redux/hooks';
import { useRouter } from 'next/navigation';
import useHomeAPI from '@/features/home/<USER>';
import CustomLink from '../../customLink/CustomLink';

const SelectStore = ({storesData,disableStoreSelection}:{storesData:any,disableStoreSelection:boolean}) => {
  const [regionDropDown, setRegionDropDown] = useState<boolean>(false);
  const { locale, region }: any = getRegionAndLocale();
  const router = useRouter();

  const { setSelectedStore } = useCommonSlice();
  const dispatch = useAppDispatch();

  const { t } = useTranslation();
  
  // #. Getting regionCode and finding the storeData
  const regionCode = region?.toUpperCase();
  const matchingNode = storesData?.stores?.edges?.find(
    ({ node }: any) => node?.country?.code === regionCode
  );

  // #. Handle Reload Router
  const handleRedirection = () => {
    router.refresh();
  };

  useEffect(() => {
    if (matchingNode) {
      const selectedStoreInfo = [
        {
          code: matchingNode?.node.country.code,
          timezone: matchingNode?.node?.timezone,
          name: matchingNode?.node?.name,
          languages: matchingNode?.node?.country?.languages?.edges,
          storeCode: matchingNode?.node?.code,
        },
      ];
      // #.Set selected store in common slice
      dispatch(setSelectedStore(selectedStoreInfo));
    }
  }, [locale, region, matchingNode]);

  const storeNameDisplay = (
    <div className={styles['selected-country']}>
      <span data-testid="selectedRegion">
        {matchingNode?.node?.country?.name}
      </span>
      <i>
        <img
          src={
            matchingNode?.node?.country?.flagImage ||
            `${imageBaseUrl}/images/flag.png`
          }
          alt="flag-img"
        />
      </i>
    </div>
  );

  /**
   * @method getStoreLinkHref
   * @param languages
   * @param country
   * @returns string
   */
  const getStoreLinkHref = (languages: any, country: any) => {
    let _locale = locale;

    // #. Supported language count 2 means, only has support on English
    if (locale === 'ar' && languages?.edges?.length < 2) {
      _locale = String(country?.defaultLanguage?.code || 'en').toLowerCase();
    }

    return `/${_locale}-${country?.code?.toLowerCase()}`;
  };

  return (
    <>
      {disableStoreSelection ? (
        <div className={styles['selected-country-name-only']}>
          {storeNameDisplay}
        </div>
      ) : (
        <div
          onMouseEnter={() => setRegionDropDown(true)}
          onMouseLeave={() => setRegionDropDown(false)}
          className={styles['stores']}
        >
          {storeNameDisplay}
          <SvgIcon icon="down-arrow" width="10" height="6" />
          <DropDownMenu width={250} dropDown={regionDropDown}>
            <div className={styles['stores-list']}>
              <div
                className={`${styles['stores-list__item']} ${styles['stores-list__item--heading']}`}
              >
                <span>{t('selectStore')}</span>
              </div>
              {storesData?.stores?.edges?.map(
                (
                  {
                    node: { languages, country, code },
                  }: { node: { languages: any; country: any; code: any } }
                ) => (
                  <CustomLink
                    prefetch={false}
                    hrefLink={getStoreLinkHref(languages, country)}
                    locale={false}
                    key={code}
                    legacyBehavior
                    aClass={`${styles['stores-list__item']}`}
                    aClick={handleRedirection}
                  >
                    <i>
                      <img src={country?.flagImage} alt={country?.code} />
                    </i>
                    <span>{country?.name}</span>
                  </CustomLink>
                )
              )}
            </div>
          </DropDownMenu>
        </div>
      )}
    </>
  );
};

export default SelectStore;
