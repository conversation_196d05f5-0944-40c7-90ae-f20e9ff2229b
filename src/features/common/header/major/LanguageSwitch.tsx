import styles from './MajorHeader.module.scss';
import React from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

import { useTranslation } from 'react-i18next';
import getRegionAndLocale from '@/utils/getRegionAndLocale';
import { updateCTProfile } from '../../clevertap/clevertap.services';
import CustomLink from '../../customLink/CustomLink';

/**
 * @method LanguageSwitch
 * @description Language switch element
 */
const LanguageSwitch = ({ languages }: { languages: string[] }) => {
  // #.Get locale and region
  const { locale, region }: any = getRegionAndLocale();
  const { t } = useTranslation();
  const router = useRouter();

  const switchLanguages = locale === 'en' ? 'ar' : 'en';
  const redirectURL = `/${switchLanguages}-${region}`;

  // #. reload router
  const handleReload = () => {
    try {
      // #. Update language in CleverTap Profile
      updateCTProfile(switchLanguages, region);
      router.push('/');
    } catch (error) {
      console.log('error in handleReload', error);
    }
  };

  return (
    <>
      {languages?.length > 1 && (
        <div className={styles['major-header__links-language-switch']}>
          <CustomLink hrefLink={redirectURL} aClass={styles['language-switch']} aClick={handleReload} legacyBehavior prefetch={false}>
            {t('switchLanguage')}
          </CustomLink>
        </div>
      )}
    </>
  );
};

export default LanguageSwitch;
