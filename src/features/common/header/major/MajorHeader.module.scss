@import '@/styles/abstracts/abstracts';

.major-header {

  &__container {
    height: 100px;
    display: flex;
    justify-content: space-between;
    padding-bottom: rem(16);
    position: relative;
  }

  &__nav {
    display: flex;
    align-items: flex-end;
    gap: 32px;
    &-logo {
      img {
        width: 74.8px;
        height: 50px;
        object-fit: contain;
      }
    }
  }

  &__links {
    display: flex;
    gap: 40px;
    align-items: flex-end;
    line-height: 1.3;
    position: relative;

    &-language-switch {
      cursor: pointer;
      position: absolute;
      top: 27px;
      right: 0px;
      font-family: var(--font-noto-kufi);

      @include rtl-styles {
        right: unset;
        left: 0;
        font-family: var(--font-mona-sans);
      }

      .language-switch {
        font-family: var(--font-noto-kufi);
        font-size: rem(14);
        font-weight: 600;
        color: $semi-dark-purple;
      }
    }

    a {
      display: flex;
      align-items: center;
      gap: 10px;
      font-size: rem(14);
      font-weight: 500;
      color: $black;
    }

    &-login {
      display: flex;
      gap: 10px;
      a {
        font-size: rem(14);
        font-weight: 500;
        color: $title-color;
        padding: 0;
      }
    }
  }

  &__account-details {
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    margin-bottom: -18px;
    .details-info {
      height: 50px;
      .account-title {
        font-size: rem(14);
        color: $title-color;
        font-weight: 500;
        width: 100%;
        text-align: end;
      }

      .user-welcome-title {
        color: $text-grey;
        font-size: rem(10);
      }
    }
  }

  .selected-country {
    display: flex;
    justify-content: flex-end;
    color: $dark-purple;
    flex-direction: row-reverse;
    gap: 5px;
    font-weight: 500;
    color: $black;

    i {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 53px;
  
  
      @include rtl-styles {
        width: 20px;
        margin-left: 0;
        margin-right: 3px;
      }
  

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border: 2px solid $subtle-grey;
      border-radius: 100%;
    }
    }
  }

  .stores {
    position: relative;
    display: flex;
    align-items: center;
    gap: 9px;
    cursor: pointer;

    &-list {
      padding: rem(19) rem(15) rem(14) rem(15);
      &__item--heading {
        margin-bottom: 14px;
        font-size: rem(14);
        font-weight: 500;
        color: $dark-grey;
      }
      &__item {
        &:not(&--heading) {
          padding: 8px 6px;
          margin-bottom: 5px;
          border-radius: 6px;
          display: grid;
          grid-template-columns: 25px auto;
          font-size: rem(14);
          align-items: baseline;
          cursor: pointer;

          &:hover {
            background-color: rgba($light-purple, 1);
            font-weight: 500;
          }
        }

        i {
          img {
            width: 16px;
            height: 12px;
            object-fit: cover;
            object-position: left;
            border-radius: 2px;
          }
        }
      }
    }
  }
}

// user account dropdown menu
.user-logout {
  height: 60px;
  display: flex;
  padding-left: rem(30);

  @include rtl-styles {
    padding-left: 0;
    padding-right: rem(30);
  }

  &__menu {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    span {
      color: $black;
      font-size: rem(14);
    }
  }
}
