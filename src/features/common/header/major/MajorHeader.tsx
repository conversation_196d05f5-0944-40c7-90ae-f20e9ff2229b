'use client';
import React, { useEffect, useState } from 'react';
import styles from './MajorHeader.module.scss';
import SvgIcon from '@/features/common/svgIcon/SvgIcon';
import Link from 'next/link';
import LanguageSwitch from './LanguageSwitch';
import StoreSelectore from './StoreSelectore';
import {
  BRAND_FILTER_TYPE,
  HEADER_TYPE,
  HELP_CENTER_URL,
  HELP_CENTER_URL_AR,
  PAGEURLS,
} from '@/constants/common';
import { imageBaseUrl } from '@/constants/envVariables';
import { useTranslation } from 'react-i18next';
import { ecomRedirectionBaseUrl } from '@/constants/apiEndPoints';
import { useRouter } from 'next/navigation';
import DropDownMenu from '../../dropDown/DropDownMenu';
import getRegionAndLocale from '@/utils/getRegionAndLocale';
import useLoginRedirection from '@/utils/loginRedirection';
import { useAppDispatch } from '@/redux/hooks';
import useCommonSlice from '../../commonSlice';
import useHomeAPI from '@/features/home/<USER>';
import useProfileAPI from '../../profileAPI';
import { updateCTProfile } from '../../clevertap/clevertap.services';
import CustomLink from '../../customLink/CustomLink';

/**
 * @method MajorHeader
 * @description MajorHeader component
 * @returns {JSX.Element}
 */
const MajorHeader = ({
  userInfo,
  disableStoreSelection,
}: {
  userInfo: {};
  disableStoreSelection?: boolean;
}): JSX.Element => {
  const { t } = useTranslation();
  const router = useRouter();
  const dispatch = useAppDispatch();
  const { setStores } = useCommonSlice();
  const { userAttributes, refreshToken }: any = userInfo;

  // #.Get locale and region
  const { locale, region }: any = getRegionAndLocale();

  // #.Login redirection custom hook
  const { redirect } = useLoginRedirection();

  const { RevokeTokens } = useProfileAPI();

  const [logoutDropDown, setLogoutDropDown] = useState<boolean>(false);

  // #. Home API calls
  const { useHeaderData, useStoresData, useSiteConfig } = useHomeAPI();
  const { data } = useHeaderData();
  const { data: stores } = useStoresData();
  const { data: siteConfigData } = useSiteConfig(region);

  // #. Minor header API filtered values
  const filteredData = data?.headers?.edges?.filter(
    ({ node }: any) => node?.headerType?.code === HEADER_TYPE.MAJOR
  );

  // #. Helpline links
  const HELPLINE_URL = locale === 'ar' ? HELP_CENTER_URL_AR : HELP_CENTER_URL;

  /**
   * Login Action
   */
  const handleLogin = () => {
    redirect(locale, region);
  };

  /**
   * Logout Action
   */
  const handleLogout = async () => {
    try {
      const response = await RevokeTokens(refreshToken);
      sessionStorage.removeItem('rewards-corporate-uuid');
      if (response?.data?.authRevokeTokens?.error?.length > 0) {
        console.error(
          'Error signing out ',
          response?.data?.authRevokeTokens?.error[0]
        );
      }
    } catch (error) {
      console.error('Error signing out ', error);
    }
    gotoHomePage();
  };

  /**
   * Redirect to home page
   */
  const gotoHomePage = () => {
    router.push(`/`, undefined);
    window.location.reload();
  };

  const languages = siteConfigData?.siteConfigs?.edges[0]?.node?.languages;
  const headerData = data?.headers;
  const storesData = stores;
  const logo = headerData?.edges[1]?.node?.logo;

  useEffect(() => {
    if (storesData) dispatch(setStores(storesData));
  }, []);

  useEffect(() => {
    // #. Update language in CleverTap Profile
    setTimeout(() => {
      updateCTProfile(locale, region);
    }, 700);
  }, [locale, region]);

  return (
    <div className={styles['major-header']}>
      <div className={`container ${styles['major-header__container']}`}>
        <div className={styles['major-header__nav']}>
          <div className={styles['major-header__nav-logo']}>
            <a href={`${ecomRedirectionBaseUrl}${locale}-${region}`}>
              <img
                src={logo || `${imageBaseUrl}/images/ygag-logo.png`}
                alt="YOUGotaGift"
              />
            </a>
          </div>
          <StoreSelectore
            storesData={storesData}
            disableStoreSelection={disableStoreSelection!}
          />
        </div>
        <div className={styles['major-header__links']}>
          <LanguageSwitch languages={languages} />
          <div className={styles['major-header__links-egift-card']}>
            <CustomLink
              prefetch={false}
              legacyBehavior
              hrefLink={`${ecomRedirectionBaseUrl}${locale}-${region}/${BRAND_FILTER_TYPE.CATEGORY}/${filteredData[0]?.node.eGiftCards_SeoName}${PAGEURLS.ALL_BRANDS}`}
              aClass={styles['links-option']}
            >
              {t('buyEGiftCards')}
            </CustomLink>
          </div>
          <div className={styles['major-header__links-helpline']}>
            <CustomLink
              hrefLink={HELPLINE_URL}
              aClass={styles['links-option']}
              prefetch={false}
            >
              {t('helpline')}
              <SvgIcon icon="helpline" width="20" height="20" />
            </CustomLink>
          </div>
          {!refreshToken ? (
            <div className={styles['major-header__links-login']}>
              <a onClick={handleLogin}>{t('loginOrSignUp')}</a>
              <SvgIcon icon="login" width="20" height="20" />
            </div>
          ) : (
            <div
              onMouseEnter={() => setLogoutDropDown(true)}
              onMouseLeave={() => setLogoutDropDown(false)}
              className={`account-details ${styles['major-header__account-details']}`}
            >
              <div className={styles['details-info']}>
                <span className={styles['user-welcome-title']}>
                  {t('hey')} {userAttributes?.name},
                </span>
                <div className={styles['account-title']}>
                  <span>{t('account')}</span>
                </div>
              </div>
              <SvgIcon icon="down-arrow" width="8" height="5" />

              <DropDownMenu width={270} dropDown={logoutDropDown}>
                <div className={styles['user-logout']}>
                  <div
                    onClick={handleLogout}
                    className={styles['user-logout__menu']}
                  >
                    <SvgIcon icon="logout-icon" width="15" height="15" />
                    <span>{t('logOut')}</span>
                  </div>
                </div>
              </DropDownMenu>
            </div>
          )}
        </div>
      </div>
      <div className={styles['major-header__end']}>
        <div className={styles['left']}></div>
        <div className={styles['right']}></div>
      </div>
    </div>
  );
};

export default MajorHeader;
