'use client';
import { useTranslation } from 'react-i18next';
import styles from './ContributionHeader.module.scss';
import getRegionAndLocale from '@/utils/getRegionAndLocale';
import Link from 'next/link';
import switchURL from '@/utils/switchURL';
import { imageBaseUrl } from '@/constants/envVariables';
import { updateCTProfile } from '../../clevertap/clevertap.services';
import { useEffect } from 'react';
import SvgIcon from '../../svgIcon/SvgIcon';
import CustomLink from '../../customLink/CustomLink';

const ContributionHeader = (languages?: any) => {
  // #.Get locale and region
  const { locale, region }: any = getRegionAndLocale();
  const { t } = useTranslation();
  const { redirectURL } = switchURL(locale, region);

  useEffect(() => {
    // #. Update language in CleverTap Profile
    setTimeout(() => {
        updateCTProfile(locale, region);
    }, 700);
}, [locale, region]);

  return (
    <div className={styles['contributer-header']}>
      <CustomLink hrefLink="/" prefetch={false}>
        <img src={`${imageBaseUrl}/images/icons/ygag-logo.svg`} alt="YOUGotaGift"/>
      </CustomLink>

      {languages?.languages && (
        <CustomLink hrefLink={redirectURL} prefetch={false} className='lang-switch'>
          <span>{t('abbrLang')}</span>
          <SvgIcon icon="lang" width="16" height="16"/> 
        </CustomLink>
      )}
    </div>
  );
};

export default ContributionHeader;
