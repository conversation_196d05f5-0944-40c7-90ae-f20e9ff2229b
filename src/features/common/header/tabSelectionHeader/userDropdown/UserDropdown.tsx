import { imageBaseUrl } from '@/constants/envVariables';
import React, { useEffect, useState } from 'react';
import styles from './UserDropdown.module.scss';
import DropDownMenu from '@/features/common/dropDown/DropDownMenu';
import { getInitials } from '@/utils/getInitials';
import ActionDropdown from './actionDropdown/ActionDropdown';
import useCommonSlice from '@/features/common/commonSlice';
import { useAppSelector } from '@/redux/hooks';
import isAbsoluteURL from '@/utils/isAbsoluteURL';
import S3Bucket from '@/utils/s3Bucket';

/**
 * @method UserDropdown
 * @description Componenet to render user action controls
 */
const UserDropdown = ({
  isGuestUser,
  setDropDown,
}: any) => {
  const profileIcon = `${imageBaseUrl}/images/icons/profile.svg`;

  const [profilePicture, setProfilePicture] = useState("");


  // #. Handle dropdown
  const activateDropDown = () => {
    setDropDown(true);
  };

  // #. Handle dropdown
  const deActivateDropDown = () => {
    setDropDown(false);
  };

  const fetchProfilePic = async (pictureObj: any) => {
    let picture;
    try {
      if (isAbsoluteURL(pictureObj)) {
        picture = pictureObj;
      } else if (pictureObj) {
        const s3Response = await S3Bucket.get(pictureObj, {
          level: "public",
          expires: 86400,
        });
        picture = s3Response;
      }
    } catch (error) {
      console.error("S3 Get Error:", error);
      picture = "";
    }
    setProfilePicture(picture);
  };

  const { getTokenInfo } = useCommonSlice();

  // #. Get user details
  const tokenInfo: any = useAppSelector(getTokenInfo);
  const userAttributes = tokenInfo?.userAttributes ?? {};

  const name = userAttributes?.name ?? '';
  const picture = userAttributes?.picture ?? '';

  // #. Get initials to show from user name
  const initials = getInitials(name);

  useEffect(()=>{
    fetchProfilePic(picture);
  },[]);

  return (
    <div
      onMouseEnter={activateDropDown}
      onMouseLeave={deActivateDropDown}
      className={`                
${styles['header-options__item']} 
      ${styles['header-options__item--stores-wrapper']} 
${styles['header-options__item--stores-wrapper-account-info']}`}
      id="e2eTestingWallet"
    >
      <div
        className={
              styles["header-options__item--stores-wrapper-account-info-col1"]
        }
      >
        <div
          className={
            !isGuestUser
                  ? styles["account-image"]
                  : styles["account-image--guest"]
          }
        >
          {isGuestUser || profilePicture ? (
            <img
              src={isGuestUser ? profileIcon : profilePicture}
              alt="profile picture"
            />
          ) : initials ? (
            <span>{initials}</span>
          ) : (
            <img src={profileIcon} alt="profile picture" />
          )}
        </div>
      </div>
    </div>
  );
};

export default UserDropdown;
