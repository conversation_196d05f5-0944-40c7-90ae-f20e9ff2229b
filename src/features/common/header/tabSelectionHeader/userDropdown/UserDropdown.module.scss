@import '@/styles/abstracts/abstracts';

%wallet-details-label {
    display: block;
    color: $warm-grey;
    @include font-size(10);
  
    padding-bottom: 5px;
    font-weight: 400;
  }

.header-options {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    flex-direction: column;
    position: relative;
  
    &__row2 {
      display: flex;
      gap: 30px;
      align-items: flex-end;
  
      @include rtl-styles {
        font-size: 12px;
        font-weight: 500;
      }
  
      a {
        display: flex;
        gap: 5px;
        transition: opacity 0.25s ease-in-out;
        align-items: flex-end;
      }
  
      &:hover {
        a {
          color: $dark-purple;
        }
      }
    }
  
    &__item {
      cursor: pointer;
      display: flex;
      align-items: center;
  
      &--active>span {
        color: $barney-purple;
      }
  
      &--stores-wrapper {
        position: relative;
        display: flex;
        flex-direction: row;
        gap: 5px;
        align-items: center;
  
        &-account-info {
          flex-direction: row;
          gap: 10px;
  
          &-col1 {
            display: flex;
            align-items: center;
            flex-direction: column;
            justify-content: flex-end;
            width: 100%;
          }
        }
  
        @include rtl-styles {
          padding-right: 0;
          padding-left: 20px;
          margin-left: 0;
          margin-right: 10px;
        }
  
        i.dropdown-icon {
          color: $warm-grey;
  
          @include font-size(6);
  
          @include rtl-styles {
            right: auto;
            left: 0;
            top: 10px;
          }
  
          &-account {
            top: 15px;
  
            @include rtl-styles {
              top: 18px;
            }
          }
        }
  
        &>span {
          @include font-size(10);
  
          display: block;
          text-align: right;
          color: $black-header;
  
          &:hover {
            color: $barney-purple;
          }
        }
      }
  
      &--cart {
        color: $dark-purple;
      }
  
    }
  
    &__wrapper {
      border-radius: 100px !important;
    }
  }

  .header-options {
    &__item {
      @media (max-width: ($md + 40)) {
        padding: 0;
      }
  
      &--language-switch {
        @media (max-width: ($md + 40)) {
          padding: 0;
        }
      }
  
      &--cart {
        display: flex;
        align-items: center;
        justify-content: center;
        background: $subtle-grey;
        height: 52px;
        width: 52px;
        border-radius: 50%;
  
        img {
          width: 32px;
          height: 32px;
          object-fit: contain;
        }
      }
  
      &--stores-wrapper {
        margin: 0;
  
        @include rtl-styles {
          margin-right: 0 !important;
          padding-left: 0 !important;
        }
      }
  
    }
  }
  
  .account-image {
    width: 52px;
    height: 52px;
    border-radius: 50%;
    border: 4px solid $default-bg-color;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
  
    img {
      width: 100%;
      display: block;
    }
  
    span {
      @include font-size(16);
      font-weight: 600;
      color: $dark-charcoal;
    }
  
    &--guest {
      border: none;
    }
  }
  

  .account-nav {
    &__padding-bottom-zero {
      padding-bottom: 0 !important;
    }
  
    &__padding-top-zero {
      padding-top: 0 !important;
    }
  
    &__profile-icon {
      height: 15px;
      width: 15px;
      margin-right: 10px;
    }
  
    &__item {
      @include font-size(16);
      font-style: normal;
      font-weight: 700;
      letter-spacing: -0.16px;
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
  
      > img {
        display: block;
        width: 24px;
        height: 24px;
  
        @include rtl-styles {
          transform: scaleX(-1);
        }
      }
  
      span {
        width: 16px;
        height: 16px;
        margin-left: auto;
  
        @include rtl-styles {
          margin-left: unset;
          margin-right: auto;
          transform: scaleX(-1);
        }
      }
    }
  
    &__item--icon-right {
      i {
        float: right;
  
        @include rtl-styles {
          float: left;
        }
      }
    }
  
    &__guest-signup {
      border-radius: 6px !important;
      padding: 10px !important;
      @include font-size(16);
      font-weight: 700;
      height: 50px;
    }
  
    &__guest-logout {
      margin-top: 24px;
      color: $black;
      @include font-size(16);
      font-weight: 700;
    }
  }