@import '@/styles/abstracts/abstracts';

  .dropdown-menu {
    min-width: 367px;
    padding: 36px 32px;
    box-shadow: 0px 4px 35px 0px rgba(0, 0, 0, 0.08);
    color: $dark-grey;
    border: solid 3px $default-bg-color;
    border-radius: 12px;
    margin-top: 5px;
    background: $white;
    z-index: 1;
    
    &__profile-icon {
      height: 15px;
      width: 15px;
      margin-right: 10px;
    }
  
    &__item {
      @include font-size(16);
      font-style: normal;
      font-weight: 700;
      letter-spacing: -0.16px;
      display: flex;
      align-items: center;
      gap: 8px;
      cursor: pointer;
  
      > img {
        display: block;
        width: 24px;
        height: 24px;
  
        @include rtl-styles {
          transform: scaleX(-1);
        }
      }
  
      span {
        width: 16px;
        height: 16px;
        margin-left: auto;
  
        @include rtl-styles {
          margin-left: unset;
          margin-right: auto;
          transform: scaleX(-1);
        }
      }
    }

    &__separator {
        width: 100%;
        height: 1px;
        background-color: $grey-stroke;
        margin: 20px 0;
    }
  }