import Link from 'next/link';
import { useTranslation } from 'next-i18next';
import styles from './ActionDropdown.module.scss';
import { imageBaseUrl } from '@/constants/envVariables';
import useProfileAPI from '@/features/common/profileAPI';
import { usePathname, useRouter } from 'next/navigation';
import { ecomRedirectionBaseUrl } from '@/constants/apiEndPoints';
import getRegionAndLocale from '@/utils/getRegionAndLocale';
import { PAGEURLS } from '@/constants/common';
import { useAppDispatch } from '@/redux/hooks';
import useCommonSlice from '@/features/common/commonSlice';
import CustomLink from '@/features/common/customLink/CustomLink';

/**
 * @method ActionDropdown
 * @description shows user actions dropdown
 * @returns {JSX.Element}
 */

const ActionDropdown = ({
  refreshToken,
}: {
  refreshToken: string;
}): JSX.Element => {
  // translations
  const {
    t,
    i18n: { language },
  } = useTranslation('common');

  const { locale, region }: any = getRegionAndLocale();

  const router = useRouter();
  const pathname = usePathname();
  const dispatch = useAppDispatch();
  const { setTokenInfo } = useCommonSlice();



  // #. Icons
  const profileIcon = `${imageBaseUrl}/images/icons/profile-circle.svg`;
  const logoutIcon = `${imageBaseUrl}/images/icons/logout.svg`;
  const arrowIcon = `${imageBaseUrl}/images/icons/arrow-black.svg`;

  const { RevokeTokens } = useProfileAPI();
  const handleLogout = async () => {
    try {
      const response = await RevokeTokens(refreshToken);
      sessionStorage.removeItem('rewards-corporate-uuid');
      if (response?.data?.authRevokeTokens?.error?.length > 0) {
        console.error(
          'Error signing out ',
          response?.data?.authRevokeTokens?.error[0]
        );
      }
    } catch (error) {
      console.error('Error signing out ', error);
    }
    setTimeout(()=>{
      dispatch(setTokenInfo({}));
      gotoHomePage();
    },2000);
  };

  const gotoHomePage = () => {
    if (pathname === `/${language}/`) {
      return window.location.reload();
    }
    // router.push(`/`, undefined);
    const store = `${locale}-${region}`;
    window.location.href = `${window.location.origin.toString()}/${store}/`
  };
  
  const profileUrl = `${ecomRedirectionBaseUrl}${locale}-${region}/${PAGEURLS.ACCOUNT}`;

  return (
    <div className={styles['dropdown-menu']}>
      <CustomLink hrefLink={profileUrl} legacyBehavior aClass={styles['dropdown-menu__item']} aId="e2eTestingMyWalletProfileLink">
          <img src={profileIcon} />
          {t('myAccount')}
          <span>
            <img src={arrowIcon} width={16} height={16} />
          </span>
      </CustomLink>
      <div className={styles['dropdown-menu__separator']}></div>
      <a
        onClick={() => handleLogout()}
        className={`cursor ${styles['dropdown-menu__item']}`}
        id="e2eTestingMyWalletLogoutLink"
      >
        <img src={logoutIcon} />
        {t('logOut')}
      </a>
    </div>
  );
};

export default ActionDropdown;
