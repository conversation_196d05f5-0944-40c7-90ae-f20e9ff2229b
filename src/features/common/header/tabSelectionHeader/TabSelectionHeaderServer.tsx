import { LOCALE_REGION_COOKIE, PLATFORM_TYPE } from '@/constants/common';
import {
  HEADER_QUERY,
  SITE_CONFIG_QUERY,
  STORES_QUERY,
} from '@/features/common/common.query';
import { getClient } from '@/graphql/apolloClient';
import TabSelectionHeader from './TabSelectionHeader';
import getCookie from '@/utils/getCookie';
import { cache } from 'react';
/**
 * @method fetchHeaderData
 * @description Fetch header data from server
 * @returns header data
 */
// Use React cache to prevent duplicate fetches during server rendering
const fetchHeaderData = cache(async (locale: string) => {
  try {
    const response = await getClient(locale).query({
      query: HEADER_QUERY,
      variables: {
        platformType_Code: PLATFORM_TYPE.WEB,
      },
      context: {
        clientName: 'webstore-with-cdn',
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching header data:', error);
    return null;
  }
});

/**
 * @method fetchStoresData
 * @description Fetch stores data from server
 * @returns stores data
 */
// Use React cache to prevent duplicate fetches during server rendering
const fetchStoresData = cache(async (locale: string) => {
  try {
    const response = await getClient(locale).query({
      query: STORES_QUERY,
      context: {
        clientName: 'webstore-with-cdn',
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching stores data:', error);
    return null;
  }
});

/**
 * @method fetchSiteConfig
 * @description Fetch site config data from server
 * @returns site config data
 */
// Use React cache to prevent duplicate fetches during server rendering
const fetchSiteConfig = cache(async (region: string, locale: string) => {
  try {
    const response = await getClient(locale).query({
      query: SITE_CONFIG_QUERY,
      variables: {
        platformType_Code: PLATFORM_TYPE.WEB,
        store: region ? region.toUpperCase() : '',
      },
      context: {
        clientName: 'webstore-with-cdn',
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching site config:', error);
    return null;
  }
});

/**
 * @method TabSelectionHeaderServer
 * @description Server component that fetches data and passes it to the client component
 * @returns TabSelectionHeader component with server-fetched data
 */
const TabSelectionHeaderServer = async ({
  children,
  userInfo,
  disableStoreSelection,
}: {
  children: React.ReactNode;
  userInfo?: any;
  disableStoreSelection?: boolean;
}) => {
  // Get locale and region from cookie
  const localeRegionCookie: any = getCookie(LOCALE_REGION_COOKIE);
  const localeRegion = localeRegionCookie?.value || 'en-ae';
  const region = localeRegion.split('-')[1];
  const locale = localeRegion.split('-')[0];

  // Fetch data on the server with Promise.all for parallel fetching
  const [headerData, storesData, siteConfigData] = await Promise.all([
    fetchHeaderData(locale),
    fetchStoresData(locale),
    fetchSiteConfig(region, locale),
  ]);

  // Pass data to client component
  return (
    <TabSelectionHeader
      headerData={headerData?.headers}
      storesData={storesData}
      siteConfigData={siteConfigData}
      userInfo={userInfo}
      disableStoreSelection={disableStoreSelection}
    >
      {children}
    </TabSelectionHeader>
  );
};

export default TabSelectionHeaderServer;
