'use client';
import React from 'react';
import { Skeleton } from '@mui/material';
import styles from './TabSelectionHeaderSkeleton.module.scss';

/**
 * @method TabSelectionHeaderSkeleton
 * @description Skeleton loader for the TabSelectionHeader component
 * @returns {JSX.Element}
 */
const TabSelectionHeaderSkeleton = (): JSX.Element => {
  return (
    <>
      <header className={styles['tab-selection-skeleton']}>
        <div className={styles['wrapper']}>
          <div className={styles['header']}>
            {/* Logo and Store Selector */}
            <div className={styles['header__left']}>
              <Skeleton
                variant="rectangular"
                width={130}
                height={52}
                className={styles['logo-skeleton']}
              />

            </div>

            {/* Tabs */}
            <div className={styles['tabs-skeleton']}>
              <Skeleton
                variant="rectangular"
                width={250}
                height={75}
                className={`${styles['tab-skeleton']} ${styles['gifting-tab-skeleton']}`}
              />
              <Skeleton
                variant="rectangular"
                width={250}
                height={75}
                className={`${styles['tab-skeleton']} ${styles['group-gifting-tab-skeleton']}`}
              />
              <Skeleton
                variant="rectangular"
                width={250}
                height={75}
                className={`${styles['tab-skeleton']} ${styles['happy-you-tab-skeleton']}`}
              />
            </div>

            {/* Action Icons */}
            <div className={styles['header__action-icons-skeleton']}>
              <Skeleton
                variant="rectangular"
                width={80}
                height={36}
                className={styles['login-button-skeleton']}
              />
            </div>

            {/* Work Tab */}
            <div className={styles['header__right-skeleton']}>
              <Skeleton
                variant="rectangular"
                width={140}
                height={40}
                className={styles['work-tab-skeleton']}
              />
            </div>
          </div>
        </div>
        <div className={styles['tab-selection-skeleton__spacer']} />
      </header>

      {/* Tab Content Skeleton */}
      <div className={styles['tab-content-wrapper-skeleton']}>
        <div className={styles['tab-content-skeleton']}>
          <div className={styles['tab-inner-skeleton']}>
            {/* Content will be rendered by the actual page skeleton */}
          </div>
        </div>
      </div>
    </>
  );
};

export default TabSelectionHeaderSkeleton;
