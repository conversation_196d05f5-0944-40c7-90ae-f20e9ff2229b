@import '@/styles/abstracts/abstracts';


.tab-selection {
  position: sticky;
  z-index: 1000;
  top: 0;
  background: white;

  &__spacer {
    display: block;
    height: 16px;
    width: 100%;
    background-color: var(--after-bg-color, transparent);
  }

  &::before,
  &::after {
    content: "";
    height: 24px;
    width: 24px;
    background: transparent;
    position: absolute;
    bottom: -24px;
    border-top-left-radius: 50px;
    box-shadow: -5px -6px 0px 2px var(--after-bg-color, transparent);
  }

  &::before {
    left: 16px;
  }

  &::after {
    right: 16px;
    transform: scaleX(-1);
  }

  &--hide-pseudo::before,
  &--hide-pseudo::after {
    display: none;
  }
}

.wrapper {
  max-width: 1448px;
  margin: 0 auto;
  background: $white;
  padding: 19px 16px 0 16px;
  position: sticky;
  z-index: 1000;
  top: 0;

  @media (max-width: ( $md + 40)) {
    top: -1px;
    box-shadow: 0 3px 6px 0 rgba(92, 99, 105, 10%);
  }
}

.header {
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: space-between;

  &__left {
    display: flex;
    gap: 24px;
    align-items: center;
  }

  &__action-icons {
    display: flex;
    align-items: center;
    gap: 22px;

    .cart-wrapper {
      cursor: pointer;

      .cart-icon {
        width: 32px;
        height: 32px;
      }
    }

    .btn-wrapper {
      border-radius: 100px;
    }

    .login-btn {
      @include font-size(18);

      padding: 0 26px;
      font-family: var(--font-bricolage); font-optical-sizing: none;
      border-radius: 100px;
      height: 42px;
      display: flex;
      align-items: center;
      justify-content: center;

      @include rtl-styles {
        padding: 0 16px;

        @media (max-width: 1100px) {
          @include font-size(14);
        }
      }
    }
  }

  &__right {
    margin-left: auto;
    margin-right: 42px;
    position: relative;

    @media (max-width: 1160px) {
      margin-right: 30px;
    }

    @include rtl-styles {
      margin-left: unset;
      margin-right: auto;
      margin-left: 42px;

      @media (max-width: (1200px)) {
        margin-left: 20px;
      }

      @media (max-width: (1140px)) {
        margin-left: 30px;
      }
    }

  }
}

.logo {
  &__image {
    height: 52px;
    width: 78px;
    object-fit: contain;
  }
}

.tab-content {
  background: white;
  border-radius: 0 0 24px 24px;
  position: relative;
  &-wrapper {
    min-height: 100vh;
    padding: 0 16px 16px;
  }

  &::before{
    content: "";
    position: absolute;
    left: 0px;
    bottom: 0;
    height: 24px;
    width: 24px;
    background-color: transparent;
    z-index: 1;
    box-shadow: -5px 6px 0px 2px var(--after-bg-color);
    border-bottom-left-radius: 50px;
  }

  &::after{
    content: "";
    position: absolute;
    right: 0px;
    bottom: 0;
    height: 24px;
    width: 24px;
    background-color: transparent;
    z-index: 1;
    box-shadow: 5px 6px 0px 2px var(--after-bg-color);
    border-bottom-right-radius: 50px;
  }


  .tab-inner{
    background-color: $white;
    border-radius: 0 0 24px 24px;
  }
}


.header-options {
  &__item {
    @media (max-width: ($md + 40)) {
      padding: 0;
    }

    &--language-switch {
      @media (max-width: ($md + 40)) {
        padding: 0;
      }
    }

    &--cart {
      display: flex;
      align-items: center;
      justify-content: center;
      background: $subtle-grey;
      height: 52px;
      width: 52px;
      border-radius: 50%;

      img {
        width: 32px;
        height: 32px;
        object-fit: contain;
      }
    }

    &--stores-wrapper {
      margin: 0;

      @include rtl-styles {
        margin-right: 0 !important;
        padding-left: 0 !important;
      }
    }
  }
}

.header-options {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  flex-direction: column;
  position: relative;

  &__row2 {
    display: flex;
    gap: 30px;
    align-items: flex-end;

    @include rtl-styles {
      font-size: 12px;
      font-weight: 500;
    }

    a {
      display: flex;
      gap: 5px;
      transition: opacity 0.25s ease-in-out;
      align-items: flex-end;
    }

    &:hover {
      a {
        color: $dark-purple;
      }
    }
  }

  &__item {
    cursor: pointer;
    display: flex;
    align-items: center;

    &--active>span {
      color: $barney-purple;
    }

    &--stores-wrapper {
      position: relative;
      display: flex;
      flex-direction: row;
      gap: 5px;
      align-items: center;

      &-account-info {
        flex-direction: row;
        gap: 10px;

        &-col1 {
          display: flex;
          align-items: center;
          flex-direction: column;
          justify-content: flex-end;
          width: 100%;
        }
      }

      @include rtl-styles {
        padding-right: 0;
        padding-left: 20px;
        margin-left: 0;
        margin-right: 10px;
      }

      i.dropdown-icon {
        color: $warm-grey;

        @include font-size(6);

        @include rtl-styles {
          right: auto;
          left: 0;
          top: 10px;
        }

        &-account {
          top: 15px;

          @include rtl-styles {
            top: 18px;
          }
        }
      }

      &>span {
        @include font-size(10);

        display: block;
        text-align: right;
        color: $black-header;

        &:hover {
          color: $barney-purple;
        }
      }
    }

    &--language-switch {
      @include font-size(14);

      font-family: var(--font-noto-kufi);
      color: $barney-purple;
      padding-top: 5px;
      display: block;
      font-weight: 600;
      position: absolute;
      top: -33px;
      right: 0px;

      @include rtl-styles {
        left: 0;
        right: unset;
      }

      &:hover {
        text-decoration: underline;
      }

      @media (max-width: ($md + 40)) {
        top: 0;
      }
    }

    &--cart {
      color: $dark-purple;
    }

    &--sign-in {
      color: $dark-purple;
      background: black;
      height: 52px;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      font-weight: 800;
      font-family: var(--font-bricolage) !important;
      padding: 12px 24px !important;
      border-radius: 100px !important;

      @include rtl-styles{
        font-family: var(--font-noto-kufi) !important;
      }
    }
  }

  &__wrapper {
    border-radius: 100px !important;
  }
}


.stores {
  &-list {
    width: 316px;
    padding: 28px 0 16px;
    font-weight: 500;
    border: solid 1px $silver;
    border-radius: inherit;
    background: white;

    @media (max-height: 820px) {
      height: calc(100vh - 140px);
      overflow-y: scroll;
    }

    &__item--heading {
      @include font-size(18);
      color: $dark-grey;
      font-family: var(--font-bricolage); font-optical-sizing: none;
      font-weight: 800;
      line-height: 16px;
      letter-spacing: -0.09px;
      margin-bottom: 22px;
      padding-left: 16px;

      @include rtl-styles {
        padding-right: 16px;
        font-family: var(--font-noto-kufi);
      }
    }

    &__item {
      &:not(&--heading) {
        @include font-size(14);
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 8px;
        font-style: normal;
        font-weight: 600;
        line-height: 18px;
        letter-spacing: -0.14px;
        cursor: pointer;
        padding: 12px 16px;
        color: $dark-charcoal;

        &:hover {
          background-color: $medium-light-grey;
          color: $dark-charcoal;
        }

        &:last-child{
          margin-bottom: 0;
        }
      }

      &--store {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      i {
        display: block;
        width: 24px;
        height: 24px;

        img {
          width: 100%;
          object-fit: cover;
        }
      }
    }

    &__lang-switch {
      display: flex;
      align-items: center;
      gap: 8px;
      border: 1px solid $medium-light-grey;
      padding: 4px 8px;
      border-radius: 6px;

      &:hover {
        background-color: $white;
      }
    }
  }
}

.selected-country {
  display: flex;
  justify-content: flex-end;
  color: $dark-purple;
  flex-direction: row-reverse;
  gap: 5px;
  font-weight: 500;

  @include rtl-styles {
    width: 100%;
  }

  span {
    @include rtl-styles {
      width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  i {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 53px;


    @include rtl-styles {
      width: 20px;
      margin-left: 0;
      margin-right: 3px;
    }

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border: 2px solid $subtle-grey;
      border-radius: 100%;
    }
  }
}

.switch {
  position: relative;
  display: inline-block;
  width: 82px;
  height: 52px;    
  margin-right: 18px;

  @include rtl-styles {
    margin: 0 0 0 18px;
  }

  &-disabled {
    opacity: 0.3;
    pointer-events: none;
  }
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: $pale-grey;
  border-radius: 100px;
  transition: 0.4s;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  font-weight: bold;
  font-size: 18px;
  color: white;
  cursor: pointer;

  &__text {
    position: absolute;
    left: 18px;
    right: 12px;
    text-align: center;
    width: 100%;
    font-style: normal;
    font-weight: 600;
    line-height: 18px;
    letter-spacing: -0.14px;
    color: black;
    font-size: 14px;
    transition: 0.4s;

    @include rtl-styles {
      right: -18px;
    }
  }

  &__thumb {
    position: absolute;
    height: 32px;
    width: 32px !important;
    left: 10px;
    bottom: 10px;
    background-size: cover;
    background-position: center;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
  }
}

input:checked+.slider {
  .slider__thumb {
    transform: translateX(30px);
  }

  .slider__text {
    left: -18px;

    @include rtl-styles {
      left: 0;
      right: 18px;
    }
  }

}


.arrow {
  border-top: 1px solid rgb(217, 223, 228);
  border-right: 1px solid rgb(217, 223, 228);
  width: 16px;
  height: 16px;
  position: absolute;
  top: 2px;
  transform: rotate(-45deg);
  margin: 0 auto;
  left: 64px;
  background-color: #fff;
  z-index: 1;
  transition: all 300ms ease-in;

  @include rtl-styles {
    left: 200px;
  }
}

.solutions-hub{
  display: inline-flex;
  height: 50px;
  padding: 0px 16px;
  align-items: center;
  gap: 24px;
  flex-shrink: 0;
  background: linear-gradient(90deg, #190A21 -14.48%, #7D2F89 62.22%);
  bottom: 32px;
  right: 0px;
  z-index: 10000;
  width: 54px;
  position: absolute;
  cursor: pointer;
  border-top-left-radius: 50px;
  border-bottom-left-radius: 50px;

  @include rtl-styles {
    right: unset;
    left: 0px;
    border-top-left-radius: unset;
    border-bottom-left-radius: unset;
    border-top-right-radius: 50px;
    border-bottom-right-radius: 50px;
  }
}

.work-new{
  position: absolute;
  top: -8px;
  left: 12px;
  z-index: 100000;
  width: 46px;
  height: 18px;
  display: inline-flex;
  padding: 2px 8px;
  justify-content: center;
  align-items: center;
  gap: 10px;
  border-radius: 12px 12px 12px 2px;
  @include rtl-styles {
      border-radius: 12px 12px 2px 12px;

  }
  background: linear-gradient(0deg, rgba(255, 255, 255, 0.00) 52.5%, rgba(255, 255, 255, 0.80) 100%), linear-gradient(140deg, #F8450B 17.29%, #960A3C 70.45%);
  box-shadow: 0px 0px 4px 0px rgba(255, 255, 255, 0.15) inset;

  @include rtl-styles {
    left: unset;
    right: 12px;
  }

  span{
    color: #FFF;
    text-align: center;
    font-size: 12px;
    font-style: normal;
    font-weight: 800;
    line-height: normal;
    letter-spacing: 0.12px;
    text-transform: uppercase;

  }
}