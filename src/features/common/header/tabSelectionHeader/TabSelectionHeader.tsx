"use client"
import React, { useEffect, useRef, useState } from 'react';
import styles from './TabSelectionHeader.module.scss';
import TabSelection from './tabSelection/TabSelection';
import { useTranslation } from 'next-i18next';
import { HEADER_TYPE, PAGEURLS } from '@/constants/common';
import getRegionAndLocale from '@/utils/getRegionAndLocale';
import useLoginRedirection from '@/utils/loginRedirection';
import Button from '../../button/Button';
import { Badge } from '@mui/material';
import { imageBaseUrl, solutionsHubUrl } from '@/constants/envVariables';
import { ecomRedirectionBaseUrl } from '@/constants/apiEndPoints';
import StoreSelector from './StoreSelector';
import { updateCTProfile } from '../../clevertap/clevertap.services';
import UserDropdown from './userDropdown/UserDropdown';
import Link from 'next/link';
import { FetchBasicCartData } from '../../cartAPI';
import useCommonSlice from '../../commonSlice';
import { useAppSelector } from '@/redux/hooks';
import { WorkSelection } from './work/WorkSelection';
import CustomLink from '../../customLink/CustomLink';

interface TabSelectionHeaderInterfce {
  children: React.ReactNode;
  userInfo?: any;
  disableStoreSelection?: boolean;
  headerData?: {
    edges: Array<{
      node: {
        headerType: { code: string };
        logo?: string;
      };
    }>;
  };
  storesData?: {
    stores: {
      edges: Array<{
        node: {
          country: { code: string };
          code: string;
        };
      }>;
    };
  };
  siteConfigData?: {
    siteConfigs: {
      edges: Array<{
        node: {
          atworkEnabled: boolean;
          solutionHubEnabled: boolean;
        };
      }>;
    };
  };
}

interface TabInterface {
  label?: string;
  bgColor: string;
  link: string;
  fontColor?: string;
  bgImageUrl?: string;
  active?: boolean;
  disableCTA?: boolean;
  content?: any;
}


/**
 * @method TabSelectionHeader
 * @description Header component with tab selection
 * @returns {JSX.Element}
 */
const TabSelectionHeader = ({
  children,
  userInfo,
  disableStoreSelection,
  headerData,
  storesData,
  siteConfigData,
}: TabSelectionHeaderInterfce): JSX.Element => {
  const { t } = useTranslation('common');
  const { redirect } = useLoginRedirection();
  const { locale, region }: any = getRegionAndLocale();
  const { refreshToken }: any = userInfo;
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isNearTop, setIsNearTop] = useState(false);
  const [dropDown, setDropDown] = useState<boolean>(false);
  const contentRef = useRef<HTMLDivElement | null>(null);

  const logo = headerData?.edges[1]?.node?.logo;

  // #. flag to show/hide "at work" tab in header.
  const atWorkEnabled =
    siteConfigData?.siteConfigs?.edges[0]?.node?.atworkEnabled;
  const solutionHubEnabled =  siteConfigData?.siteConfigs?.edges[0]?.node?.solutionHubEnabled;

  const regionCode = region?.toUpperCase();
  const matchingNode = storesData?.stores?.edges?.find(
    ({ node }: any) => node?.country?.code === regionCode
  );

  const { getTokenInfo } = useCommonSlice();
  const tokenInfo: any = useAppSelector(getTokenInfo);
  const userAttributes = tokenInfo?.userAttributes ?? {};
  const { AccessToken }: any = useAppSelector(getTokenInfo);
  const { basicCartDataData } = FetchBasicCartData(
    matchingNode?.node?.code,
    AccessToken
  );

  const cartIcon = `${imageBaseUrl}/images/icons/cart-black.svg`;
  const ecomUrl = `${ecomRedirectionBaseUrl}${locale}-${region}`;
  // const backgroundImage = `${imageBaseUrl}/images/purple-bg.png`;

  // #. Minor header API filtered values
  const filteredData = headerData?.edges?.filter(
    ({ node }: any) => node?.headerType?.code === HEADER_TYPE.MAJOR
  );

  const handleLogin = () => {
    redirect(locale, region);
  };

  // #. Tab data for each page
  const tabs: TabInterface[] = [
    {
      label: t('gifting'),
      bgColor: 'var(--ecom-bg-color)',
      link: `${ecomUrl}`,
    },
    {
      label: t('groupGifting'),
      bgColor: '#D2C9FF',
      link: PAGEURLS.HOME,
    },
    {
      label: t('happyYouOffers'),
      bgColor: '#FF9B9B',
      link: `${ecomUrl}${PAGEURLS.OFFERS}`,
    },
    {
      bgColor:"white",
      link: PAGEURLS.CART,
      active: false,
      disableCTA: true,
      content: (
        <>
         <div className={styles['header__action-icons']}>
            {!isAuthenticated ? (
              <Button
                theme="primary"
                action={handleLogin}
                className={`${styles['login-btn']}`}
                wrapperClassName={styles['btn-wrapper']}
              >
                {t('login')}
              </Button>
            ) : (
              <UserDropdown
                filteredData={filteredData}
                setDropDown={setDropDown}
              />
            )}

            {basicCartDataData?.cart?.totalQuantity > 0 && (
              <CustomLink prefetch={false} hrefLink={`${ecomUrl}${PAGEURLS.CART}`} legacyBehavior>
                <div className={styles['cart-wrapper']}>
                  <Badge
                    badgeContent={basicCartDataData?.cart?.totalQuantity}
                    anchorOrigin={{
                      vertical: 'top',
                      horizontal: 'right',
                    }}
                    className="cart-badge"
                  >
                    <img className={styles['cart-icon']} src={cartIcon} />
                  </Badge>
                </div>
              </CustomLink>
            )}
          </div>
        </>
      ),
    },
    // {
    //   label: t('gaming'),
    //   bgColor: '#b801d4',
    //   link: `${ecomUrl}${
    //     PAGEURLS.CATERGORIES + PAGEURLS.GAMING_GIFT_CARD + PAGEURLS.ALL_BRANDS
    //   }/`,
    //   fontColor: 'white',
    //   bgImageUrl: backgroundImage,
    // },
  ];

  const activeTab = PAGEURLS.HOME;
  const activeTabData = tabs.find((tab) => tab.link === activeTab);

  useEffect(() => {
    document.documentElement.style.setProperty(
      '--after-bg-color',
      activeTabData?.bgColor || 'var(--ecom-bg-color)'
    );
  }, []);

  useEffect(() => {
    // #. Update language in CleverTap Profile
    setTimeout(() => {
      updateCTProfile(locale, region);
    }, 700);
  }, [locale, region]);

  // #. To fix Hydration error
  
  useEffect(() => {
    setIsAuthenticated(Object.keys(userAttributes).length > 0);
  }, [userAttributes]);

  useEffect(() => {
    const handleScroll = () => {
      if (contentRef.current) {
        const rect = contentRef.current.getBoundingClientRect();
      const isNearTop = rect.bottom <= 150;
      setIsNearTop(isNearTop);
      }
    };

    window.addEventListener('scroll', handleScroll);
    handleScroll();

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const dropdownProps = { 
    dropDown,
    refreshToken,
    setDropDown
  }

  return (
    <>
      <header className={`${styles['tab-selection']} ${isNearTop ? styles['tab-selection--hide-pseudo'] : ''}`}>
        <div className={`${styles.wrapper} ${styles.header}`}>
          <div className={`${styles['header__left']}`}>
            <a className={styles.logo} href={ecomUrl}>
              <img
                src={logo || `${imageBaseUrl}/images/ygag-logo.png`}
                alt="YOUGotaGift"
                className={styles['logo__image']}
              />
            </a>
            <StoreSelector
              storesData={storesData}
              disableStoreSelection={disableStoreSelection}
            />
          </div>
          <TabSelection tabs={tabs} activeTab={activeTab} dropdownProps={dropdownProps}/>
          <div className={styles['header__right']}>
            {
              atWorkEnabled &&
              <>
                <div className={styles['work-new']}>
                  <span>{t('new')}</span>
                </div>
                <WorkSelection isWorkTabActive={false} />
              </>
            }
          </div>
        </div>
        {solutionHubEnabled && (
          <Link href={`${solutionsHubUrl}${locale}`}>
            <div className={styles['solutions-hub']}>
              <img src={`${imageBaseUrl}/images/union.svg`} />
            </div>
          </Link>
        )}
        <div className={styles['tab-selection__spacer']} />
      </header>

      {/* Tab Content */}
      <div
        ref={contentRef}
        className={styles['tab-content-wrapper']}
        style={{
          backgroundColor: activeTabData?.bgColor || 'var(--ecom-bg-color)',
        }}
      >
        <div className={styles['tab-content']}>
          <div className={styles['tab-inner']}>{children}</div>
        </div>
      </div>
    </>
  );
};

export default TabSelectionHeader;
