'use client';
import React, { useEffect, useState } from 'react';
import styles from './TabSelectionHeader.module.scss';
import DropDownMenu from '../../dropDown/DropDownMenu';
import Link from 'next/link';
import { imageBaseUrl } from '@/constants/envVariables';
import getRegionAndLocale from '@/utils/getRegionAndLocale';
import { useTranslation } from 'react-i18next';
import useCommonSlice from '../../commonSlice';
import { useAppDispatch } from '@/redux/hooks';
import { usePathname, useRouter } from 'next/navigation';
import { updateCTProfile } from '../../clevertap/clevertap.services';
import CustomLink from '../../customLink/CustomLink';

const SelectStore = ({ storesData, disableStoreSelection }: any) => {
  const arrowIcon = `${imageBaseUrl}/images/icons/arrow.png`;
  const translateIcon = `${imageBaseUrl}/images/icons/language-switch.svg`;

  const [regionDropDown, setRegionDropDown] = useState<boolean>(false);
  const { locale, region }: any = getRegionAndLocale();
  const router = useRouter();
  const [isArabic, setIsArabic] = useState(locale === 'ar');

  const { setSelectedStore, getSelectedStore } = useCommonSlice();
  const dispatch = useAppDispatch();
  const pathname = usePathname();

  const { t } = useTranslation();

  const regionCode = region?.toUpperCase();
  const matchingNode = storesData?.stores?.edges?.find(
    ({ node }: any) => node?.country?.code === regionCode
  );

  const hasMultipleLanguages = storesData?.stores?.edges?.some(
    (storeItem: any) =>
      storeItem?.node?.country?.code === region?.toUpperCase() &&
      storeItem?.node?.languages?.edges?.length > 1
  );

  const storeNameDisplay = (
    <div className={styles['selected-country']}>
      {hasMultipleLanguages ? (
        <label className={styles['switch']}>
          <input type="checkbox" disabled checked={isArabic} />
          <span className={styles['slider']}>
            <span className={styles['slider__text']}>
              {isArabic ? 'AR' : 'EN'}
            </span>
            <span
              className={styles['slider__thumb']}
              style={{
                backgroundImage: `url('${matchingNode?.node?.country?.circularStoreLogo}')`,
              }}
            ></span>
          </span>
        </label>
      ) : (
        <i>
          {matchingNode?.node?.country?.flagImage && (
            <img
              src={matchingNode?.node?.country?.circularStoreLogo}
              alt={matchingNode?.node?.country?.name}
            />
          )}
        </i>
      )}
    </div>
  );

  const getStoreLinkHref = (languages: any, country: any) => {
    let _locale = locale;

    // #. Supported language count 2 means, only has support on English
    if (locale === 'ar' && languages?.edges?.length < 2) {
      _locale = String(country?.defaultLanguage?.code || 'en').toLowerCase();
    }

    return `/${_locale}-${country?.code?.toLowerCase()}`;
  };

  const handleRedirect = (
    e: React.MouseEvent<HTMLDivElement>,
    country: any
  ) => {
    e.preventDefault();
    setRegionDropDown(false);

    const newLocale = locale === 'en' ? 'ar' : 'en';
    const countryCode = country?.code?.toLowerCase();

    const updatedPath = pathname.replace(
      /^\/(en|ar)-\w+/,
      `/${newLocale}-${countryCode}`
    );

    updateCTProfile(newLocale, region);

    window.location.href = updatedPath;
  };

  useEffect(() => {
    if (matchingNode) {
      const selectedStoreInfo = [
        {
          code: matchingNode?.node.country.code,
          timezone: matchingNode?.node?.timezone,
          name: matchingNode?.node?.name,
          languages: matchingNode?.node?.country?.languages?.edges,
          storeCode: matchingNode?.node?.code,
        },
      ];
      // #.Set selected store in common slice
      dispatch(setSelectedStore(selectedStoreInfo));
    }
  }, [locale, region, matchingNode]);

  useEffect(() => {
    setIsArabic(locale === 'ar');
  }, [locale]);

  return (
    <>
      {disableStoreSelection ? (
        <div className={styles['selected-country-name-only']}>
          {storeNameDisplay}
        </div>
      ) : (
        <div
          onMouseEnter={() => setRegionDropDown(true)}
          onMouseLeave={() => setRegionDropDown(false)}
          onClick={() => setRegionDropDown(true)}
          className={`
            ${styles['header-options__item']} 
            ${styles['header-options__item-container']} 
              ${styles['header-options__item-with-hover']} 
              ${styles['header-options__item--stores-wrapper']} 
              ${regionDropDown ? styles['header-options__item--active'] : ''}`}
        >
          {storeNameDisplay}
          <DropDownMenu
            width={316}
            dropDown={regionDropDown}
            hasMultipleLanguages={hasMultipleLanguages}
          >
            <>
              {regionDropDown && (
                <img className={styles.arrow} src={arrowIcon} />
              )}
              <div className={styles['stores-list']}>
                <div
                  className={`${styles['stores-list__item']} ${styles['stores-list__item--heading']}`}
                >
                  <span>{t('selectStore')}</span>
                </div>
                {storesData?.stores?.edges?.map(
                  ({
                    node: { languages, country, code },
                  }: {
                    node: { languages: any; country: any; code: any };
                  }) => (
                    <CustomLink
                      prefetch={false}
                      hrefLink={getStoreLinkHref(languages, country)}
                      locale={false}
                      key={code}
                      legacyBehavior
                      aClass={`${styles['stores-list__item']}`}
                      aTitle={`${country?.name}`}
                    >
                        <div className={styles['stores-list__item--store']}>
                          <i>
                            <img
                              src={country?.circularStoreLogo}
                              alt={country?.code}
                            />
                          </i>
                          <span>{country?.name}</span>
                        </div>

                        {languages?.edges?.length > 1 && (
                          <div
                            className={styles['stores-list__lang-switch']}
                            onClick={(e) => handleRedirect(e, country)}
                          >
                            {t('abbrLang')}
                            <img src={translateIcon} alt={'lang'} />
                          </div>
                        )}
                    </CustomLink>
                  )
                )}
              </div>
            </>
          </DropDownMenu>
        </div>
      )}
    </>
  );
};

export default SelectStore;
