import { useTranslation } from "next-i18next";
import styles from "./TabSelection.module.scss";
import Link from "next/link";
import { useEffect, useState } from "react";
import Marquee from "react-fast-marquee";
import getRegionAndLocale from "@/utils/getRegionAndLocale";
import CustomLink from "@/features/common/customLink/CustomLink";
import DropDownMenu from "@/features/common/dropDown/DropDownMenu";
import ActionDropdown from "../userDropdown/actionDropdown/ActionDropdown";
import { PAGEURLS } from "@/constants/common";

/**
 * @method TabSelectionHeader
 * @description Handles tab button selection and page routing.
 * @returns {JSX.Element}
 */
const TabSelection = ({ tabs, activeTab, dropdownProps }: any): JSX.Element => {
  // #. Get current screen width
  const [screenWidth, setScreenWidth] = useState<any>();

  // #. Taranslation
  const { t } = useTranslation("common");

  const { locale } = getRegionAndLocale();

  useEffect(() => {
    setScreenWidth(window.innerWidth);
  }, [screenWidth]);

  const { dropDown, refreshToken, setDropDown } = dropdownProps;

  /**
   * @method getCurveClasses
   * @description: This handles the logic for adding curved psudo element classes to the tabs left and right of the active tab. (for the white border between each tab intersections)
   * @returns {string}
   */
  const getCurveClasses = (
    index: number,
    activeIndex: number,
    tabsLength: number
  ) => {
    const classes = [];
  
    const AFTER_RANGE = Math.min(4, tabsLength - 1);
    const BEFORE_RANGE = Math.min(3, tabsLength - 1);

    const isWrapAround =
      (activeIndex === tabsLength - 1 && index === 0) ||
      (activeIndex === 0 && index === tabsLength - 1);

    const isFirstAfter =
      index === activeIndex + 1 && activeIndex !== tabsLength - 1; // only if active is NOT last

    if (isFirstAfter) {
      classes.push(styles.borderBothSides);
    } else if (
      (index > activeIndex && index <= activeIndex + AFTER_RANGE) ||
      (index < activeIndex && index >= activeIndex - BEFORE_RANGE) ||
      isWrapAround ||
      (activeIndex === tabsLength - 1 && index === activeIndex - 1) // left-only for last tab
    ) {
      classes.push(styles.borderLeftOnly);
    }
    return classes.join(" ");
  };

  return (
    //   {/* Tab Buttons */}
    <div className={styles.tabs}>
      {tabs.map((tab: any, index: number) => {
        // Determine the index of the active tab
        let activeIndex = tabs.findIndex((t: any) => t.link === activeTab);
        activeIndex = activeIndex === -1 ? 0 : activeIndex;

        const validActiveTab = tabs[activeIndex].link;

        // Calculate the distance from the active tab
        const distance = Math.abs(index - activeIndex);

        // Set the z-index:
        // 1. If it's the active tab, give it the highest z-index.
        // 2. For other tabs, calculate z-index based on distance (wrap around the tabs).
        let zIndex;

        if (index === activeIndex) {
          zIndex = 99999;
        } else {
          zIndex = index + 1;
        }

        let isCartTab = tab.link === PAGEURLS.CART;

        return (
          <CustomLink key={tab.link} hrefLink={tab.link} disableCTA={tab?.disableCTA} shallow legacyBehavior
            aClass={`${styles.tab} ${validActiveTab === tab.link ? styles.linkActive : ""}`}>
            <div
              key={tab.link}
              className={`${styles.tab} ${
                validActiveTab === tab.link ? styles.active : ""
              } ${isCartTab ?styles['cart-tab'] : ""}`}
              style={{
                backgroundColor: tab.bgColor,
                ...(tab?.fontColor && { color: tab.fontColor }),
                opacity: validActiveTab === tab.link ? 1 : 0.8,
                zIndex: zIndex,
                backgroundImage: tab.bgImageUrl ? `url(${tab.bgImageUrl})` : "",
                backgroundPosition: "center",
              }}
            >
              <div
                className={`${styles.curve} ${getCurveClasses(
                  index,
                  activeIndex,
                  tabs.length
                )}`}
              />
              {tab?.content ? (
                tab?.content
              ) : (
                <h1>
                  {(screenWidth <= 1360 && tab.label?.length > 8) ||
                  (locale == 'ar' && tab.label?.length >= 13) ? (
                    <Marquee
                      className={styles.marquee}
                      gradient={true}
                      gradientColor={tab.bgColor}
                      gradientWidth={10}
                    >
                      {tab.label === t('work') ? (
                        <>
                          <span>@</span>
                          {tab.label}
                        </>
                      ) : (
                        tab.label
                      )}
                      <span style={{ marginRight: '18px' }}></span>
                    </Marquee>
                  ) : (
                    <>
                      {tab.label === t('work') ? (
                        <>
                          <span>@</span>
                          {tab.label}
                        </>
                      ) : (
                        tab.label
                      )}
                    </>
                  )}
                </h1>
              )}
            </div>
          </CustomLink>
        );
      })}

     <DropDownMenu
        className="drop-down-user-info"
        maxWidth={250}
        dropDown={dropDown}
        setDropDown={setDropDown}
      >
        <ActionDropdown refreshToken={refreshToken} />
      </DropDownMenu>
    </div>
  );
};

export default TabSelection;
