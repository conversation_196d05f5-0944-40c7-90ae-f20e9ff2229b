import Link from "next/link";
import React from "react";
import styles from "./WorkSelection.module.scss";
import { useTranslation } from "next-i18next";
import { PAGEURLS } from "@/constants/common";
import getRegionAndLocale from "@/utils/getRegionAndLocale";
import { useAppSelector } from "@/redux/hooks";
import useCommonSlice from "@/features/common/commonSlice";
import { atWorkUrl } from "@/constants/envVariables";
import CustomLink from "@/features/common/customLink/CustomLink";

/**
 * @method WorkSelection
 * @description work selection tab component
 * @returns {JSX.Element}
 */

export const WorkSelection = ({
  isWorkTabActive,
}: {
  isWorkTabActive: boolean;
}): JSX.Element => {
  // #. Get translations
  const { t } = useTranslation('common');

  const { getSelectedStore } = useCommonSlice();

  const { locale, region } = getRegionAndLocale();
  const slectedStore: any = useAppSelector(getSelectedStore);
  const activeRegion = slectedStore?.find(
    ({ code }: any) => code === region?.toUpperCase()
  );

  // #. Get the current region
  const currentRegionCode =
  activeRegion?.code?.toLowerCase() || 'ae';

  const atWorkSiteUrl = `${atWorkUrl}${PAGEURLS.WORK}${locale}-${currentRegionCode}`;

  return (
    <CustomLink hrefLink={atWorkSiteUrl} shallow>
      <div
        className={
          isWorkTabActive
            ? `${styles['tab-selection-work']} ${styles['tab-selection-work--active']}`
            : styles['tab-selection-work']
        }
      >
        <div
          className={`${styles['tab-selection-work__extension']} ${styles['tab-selection-work__extension--right']}`}
        />
        <h1>
          <span>@</span>
          {t('work')}
        </h1>
        {isWorkTabActive && (
          <div
            className={`${styles['tab-selection-work__extension']} ${styles['tab-selection-work__extension--left']}`}
          />
        )}
      </div>
    </CustomLink>
  );
};
