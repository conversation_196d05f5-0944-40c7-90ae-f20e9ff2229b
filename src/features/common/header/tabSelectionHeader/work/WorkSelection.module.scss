@import '@/styles/abstracts/abstracts';

.tab-selection {
    &-work {
        height: 76px;
        border-radius: 100px 100px 0 100px;
        background: #99C6FF;
        width: 188px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        border-bottom: 3px solid $white;
        cursor: pointer;
        z-index: 999;

        >h1 {
            @include font-size(24);
            font-family: var(--font-bricolage); font-optical-sizing: none;
            text-transform: capitalize;
            text-align: center;
            width: 100%;
            font-weight: 800;

            @include rtl-styles {
                font-family: var(--font-noto-kufi) !important;
                direction: ltr;
            }

            span {
                font-family: var(--font-mona-sans) !important;
                @include font-size(24);
                font-style: normal;
                font-weight: 800;
                line-height: 40px;
            }
        }

        @include rtl-styles {
            border-radius: 100px 100px 100px 0;
        }

        @media (max-width: ($xl + 40)) {
            width: 158px;
        }

        @media (max-width: ($lg + 40)) {
            width: 150px;
        }

        @media (max-width: 1200px) {
            width: 133px;
        }

        &--active {
            border-radius: 100px 100px 0 0 / 100px 100px 0 100px;
            border-bottom: none;

            @include rtl-styles {
                border-radius: 100px 100px 0 0 / 100px 100px 100px 100px;
                font-family: var(--font-mona-sans) !important;
            }
        }

        &__extension {
            height: 36px;
            width: 36px;
            background: #99C6FF;
            display: flex;
            align-items: flex-end;
            justify-content: flex-start;
            overflow: hidden;
            position: absolute;
            bottom: 0;

            &--right {
                right: -36px;

                @include rtl-styles {
                    right: auto;
                    left: -36px;
                }

                &::after {
                    content: "";
                    display: block;
                    height: 36px;
                    width: 36px;
                    background: white;
                    border-bottom-left-radius: 36px;

                    @include rtl-styles {
                        transform: scaleX(-1);
                    }
                }
            }

            &--left {
                left: -36px;

                @include rtl-styles {
                    right: -36px;
                    left: auto;
                }

                &::before {
                    content: "";
                    display: block;
                    height: 36px;
                    width: 36px;
                    background: white;
                    border-bottom-right-radius: 36px;

                    @include rtl-styles {
                        transform: scaleX(-1);
                    }
                }
            }
        }
    }
}