@import '@/styles/abstracts/abstracts';

.tab-selection-skeleton {
  position: sticky;
  z-index: 1000;
  top: 0;
  background: white;

  &__spacer {
    display: block;
    height: 16px;
    width: 100%;
    background-color: #f0f0f0; 
  }
}

.wrapper {
  max-width: 1448px;
  margin: 0 auto;
  background: $white;
  padding: 19px 16px 0 16px;
  position: sticky;
  z-index: 1000;
  top: 0;
}

.header {
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: space-between;
  gap: 40px;
  &__left {
    display: flex;
    gap: 24px;
    align-items: center;

    .logo-skeleton {
      border-radius: rem(4);
      background-color: #f0f0f0; /* Light gray for logo */
      opacity: 0.8;
    }
  }

  &__action-icons-skeleton {
    display: flex;
    align-items: center;
    gap: 22px;
    margin-left: 36px;

    @include rtl-styles {
      margin: 0 36px 0 0;
    }

    .login-button-skeleton {
      border-radius: 4px;
      opacity: 0.9;
    }
  }

  &__right-skeleton {
    margin-left: auto;
    margin-right: 10px;

    .work-tab-skeleton {
      border-radius: rem(20);
      opacity: 0.9;
    }

    @media (max-width: 1160px) {
      margin-right: 30px;
    }

    @include rtl-styles {
      margin-left: unset;
      margin-right: auto;

      @media (max-width: (1200px)) {
        margin-left: 20px;
      }

      @media (max-width: (1140px)) {
        margin-left: 30px;
      }
    }
  }
}

.tabs-skeleton {
  display: flex;
  align-items: center;
  justify-content: center;
  gap:5px;

  .tab-skeleton {
    border-radius: 24px 24px 0 0;
    height: 60px;
    width: 250px;

    @media (max-width: $xxl) {
      width: 230px;
    }

    @media (max-width: $xl) {
      width: 210px;
    }

    @media (max-width: ($lg - 10)) {
      width: 190px;
    }

    @media (max-width: 1160px) {
      width: 170px;
    }
  }

  .gifting-tab-skeleton {
    opacity: 0.9;
  }

  .group-gifting-tab-skeleton {
    opacity: 0.9;
  }

  .happy-you-tab-skeleton {
    opacity: 0.9;
  }
}

.tab-content-skeleton {
  background: white;
  border-radius: 0 0 24px 24px;
}

.tab-inner-skeleton {
  background-color: $white;
  border-radius: 0 0 24px 24px;
}
