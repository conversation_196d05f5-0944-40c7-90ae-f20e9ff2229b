

export function pushCleverTapEvent(eventName: string, eventData: {}) {
  try {
    typeof window !== 'undefined' &&
      window?.clevertap?.event?.push(eventName, eventData);
  } catch (err) {
    console.log('error in CT pushEvent', err);
  }
}

export function updateCTProfile(lang: string, store: string) {
  try {
    typeof window !== 'undefined' &&
      window?.clevertap &&
      window?.clevertap?.profile?.push({
          Site: {
              Language: lang,
              Store: store
          },
      });
  } catch (err) {
    console.log('error in CT updateCTProfile', err);
  }
}
