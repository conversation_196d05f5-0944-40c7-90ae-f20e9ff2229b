'use client';

import React, { useEffect, useState } from 'react';
import Loader from '../loader/Loader';
import CookieDisabled from '../cookieDisabled/CookieDisabled';
import cookieIsEnabled from '@/utils/cookieEnabled';

/**
 * @method CookieCheck
 * @description This is a wrapper element used to check if browser storage permissions are enabled
 * @param children
 */
const CheckCookie = ({ children }: any) => {
  //  #. State to manage cookie status
  const [isEnabled, setisEnabled] = useState<any>(true);

  useEffect(() => {
    if (cookieIsEnabled()) {
      setisEnabled(true);
    } else {
      setisEnabled(false);
    }
  }, []);

  return (
    <>
      {isEnabled === 'loading' ? (
        <Loader />
      ) : isEnabled === true ? (
        children
      ) : (
        <CookieDisabled />
      )}
    </>
  );
};

export default CheckCookie;
