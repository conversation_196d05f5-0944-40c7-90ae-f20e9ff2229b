import { Drawer, TextField } from '@mui/material';
import styles from './ContributeDrawer.module.scss';
import { useState } from 'react';
import Button from '../button/Button';
import useGuestUserSlice from '@/features/gifts/giftsOpen/guestUserSlice';
import { useAppDispatch } from '@/redux/hooks';
import { useTranslation } from 'react-i18next';

interface ContributeInterface {
  open: boolean;
  onClose?: () => void;
  contributingName: string;
}

const ContributeDrawer = ({
  open = false,
  onClose,
  contributingName,
}: ContributeInterface) => {
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { setGuestUserName } = useGuestUserSlice();
  const [name, setName] = useState(contributingName);
  const [nameValidate, setNameValidate] = useState('');

  /**
   * @method handleChange
   * @description Handle handleChange name change
   * @param event
   */
  const handleChange = (event: any) => {
    const inputValue = event.target.value.trimStart();
    const textToValidate = inputValue?.replace(/\s/g, '');
    setName(inputValue);

    if (!inputValue) {
      setNameValidate(t('notEmpty'));
    } else if (textToValidate?.length < 2) {
      setNameValidate(t('FieldErrorMsg'));
    } else {
      setNameValidate('');
    }
  };

  /**
   * @method handleDone
   * @description Handle Done button action
   */
  const handleDone = () => {
    dispatch(setGuestUserName(name));
    onClose && onClose();
  };

  return (
    <Drawer
      anchor={'bottom'}
      open={open}
      onClose={onClose}
      PaperProps={{ style: { position: 'absolute' } }}
      BackdropProps={{ style: { position: 'absolute' } }}
      ModalProps={{
        container: document.getElementById('drawer-container'),
        style: { position: 'absolute' },
      }}
      className="contribute-drawer"
    >
      <div className={styles['contribute']}>
      <div className={styles['drawer-handle']}/>
        <h4 className={styles['contribute__title']}>{t('contributingAs')}</h4>
        <p className={styles['contribute__sub-title']}>
          {t('contributeDescription')}
        </p>
        <TextField
          value={name}
          autoFocus
          fullWidth
          variant="outlined"
          inputProps={{
            maxLength: 100,
            min: 2,
          }}
          helperText={nameValidate}
          onChange={handleChange}
          className={styles['contribute__input']}
        />
        <div className={`${styles['contribute__button-wrapper']} ${nameValidate ? styles['contribute__button-wrapper--error'] : ''}`}>
          <Button
            theme="secondary"
            className={`${styles['contribute__button--close']}`}
            action={onClose}
          >
            {t('close')}
          </Button>

          <Button
            theme="primary"
            className={styles['contribute__button--continue']}
            action={handleDone}
            attribues={{
              disabled: nameValidate,
              type: 'button',
            }}
          >
            {t('done')}
          </Button>
        </div>
      </div>
    </Drawer>
  );
};

export default ContributeDrawer;
