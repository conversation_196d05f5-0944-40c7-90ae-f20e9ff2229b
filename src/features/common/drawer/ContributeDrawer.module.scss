@import '@/styles/abstracts/abstracts';

.contribute {
  padding: 40px 20px 16px;
  position: relative;

  .drawer-handle{
    width: 48px;
    height: 3.6px;
    background-color: $grey-stroke;
    border-radius: 12px;
    position: absolute;
    top: 12px;
    left: 50%;
    transform: translateX(-50%);
  }

  &__title {
    font-size: rem(24);
    color: $dark-charcoal;
    margin-bottom: 16px;
    font-weight: 800;
    line-height: 28px;
    font-family: var(--font-bricolage); font-optical-sizing: none;
  }
  &__sub-title {
    font-size: rem(16);
    font-family: var(--font-bricolage); font-optical-sizing: none;
    color: $dark-charcoal !important;
    margin-bottom: 24px;
    font-weight: 600;
    line-height: 24px;
  }

  &__input {
    height: 50px;

    input{
      padding: 13px 16px;
    }
  }
  &__button-wrapper {
    display: flex;
    justify-content: space-between;
    margin-top: 24px;
    gap: 16px;

    &--error{
      margin-top: 46px;
    }
  }
  &__button--close {
    height: 50px !important;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 700;
    line-height: 24px;
    letter-spacing: -0.16px;
  }

  &__button--continue {
    height: 50px !important;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 700;
    line-height: 24px;
    letter-spacing: -0.16px;
  }
}
