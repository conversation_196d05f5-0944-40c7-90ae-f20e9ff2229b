'use client';
import React, { useEffect, useRef, useState } from 'react';
import { useAppDispatch } from '@/redux/hooks';
import useCommonSlice from '../commonSlice';
import { getCookie, setCookie } from '@/utils/getAndsetCookie';
import { EGG_REWARDS_REFRESH_TOKEN, EGG_REWARDS_SITE_DOMIAN, IS_GUEST_USER_COOKIE } from '@/constants/common';
import useGiftsAPI from '@/features/gifts/giftsAPI';
import useRewardsAPI from '../rewardsAPI';
import { useRouter, useSearchParams } from 'next/navigation';
import Loader from '../loader/Loader';
import { HAS_WINDOWS_REF } from '@/utils/hasWindowRef';
import { clearRewardsCookie, clearSession } from './authAPI';
import { rewardsAppRedirectUrl } from '@/constants/envVariables';
import { HomePageSkeleton } from '@/features/home/<USER>';

interface AuthenticationProviderClientProps {
  userInfo: any;
  guestTokens: any;
  useSkeletonLoader?: boolean;
  cookieLocale?: any;
}

/**
 * @method AuthenticationProviderClient
 * @description Client component that handles authentication state and effects
 * This component renders loading states when needed
 */
const AuthenticationProviderClient = ({
  userInfo,
  guestTokens,
  useSkeletonLoader = false,
  cookieLocale
}: AuthenticationProviderClientProps) => {
  const {
    refreshToken,
    idToken,
    accessToken,
    isUserSignedIn,
    displayName,
    userAttributes,
    isGuestUser,
    isRewardsUser,
    isReload
  }: any = userInfo;

  const { setTokenInfo, setGuestInfo } = useCommonSlice();
  const dispatch = useAppDispatch();
  const searchParams = useSearchParams();
  const { FetchGuestAccessToken } = useGiftsAPI();
  const { groupGiftRewardsAuth } = useRewardsAPI();
  const [trigger, setTrigger] = useState<any>(false);
  const [isReloadPage, setisReloadPage] = useState<boolean>(false);
  const [inIframe, setInIfram] = useState(false);
  const isRewards = searchParams.get('embedded');
  const rewCorporateUuid: any = searchParams.get('tracker_ref');
  const router = useRouter();
  const isInIframe = HAS_WINDOWS_REF && (window.location !== window.parent.location);
  const rewardsSiteDomain = getCookie(EGG_REWARDS_SITE_DOMIAN);
  const rewardsRefreshToken = getCookie(EGG_REWARDS_REFRESH_TOKEN);

  // Remove surrounding double quotes if they exist
  const sanitizedRewardsSiteDomain = rewardsSiteDomain.replace(/^"|"$/g, '');
  const rewardsAppHomeUrl = `${sanitizedRewardsSiteDomain}/corporate/gift-cards/`;
  const rewardsLoginUrl = `${rewardsAppRedirectUrl}account/login/identifier/`;
  const rewardsClearCookie = getCookie("rewards-clear");
  const hasRedirected = useRef(false);

  useEffect(() => {
    if (!isInIframe && !!isRewards) {
      setInIfram(true);
    } else {
      setInIfram(false);
    }
  }, [isRewards]);

  useEffect(() => {
    if(HAS_WINDOWS_REF && isReloadPage && isReload){
      setisReloadPage(false);
      window.location.reload();
    }
  }, [isReloadPage, isReload]);

  useEffect(() => {
    clearSessionCall();
  }, []);

  useEffect(() => {
    if(rewCorporateUuid) {
      sessionStorage.setItem("rewards-corporate-uuid", rewCorporateUuid);
    }
  }, [rewCorporateUuid]);

  useEffect((): any => {
    // ## Checking if Iframe is from Ecom Web
    if(window.top && !hasRedirected.current && isInIframe && !rewardsRefreshToken){
      hasRedirected.current = true;
      window.top.location.href = rewardsLoginUrl;
    }
  }, []);

  useEffect(() => {
    if (HAS_WINDOWS_REF) {
      window.addEventListener('storage', function (event) {
        if (event.key == 'rewards-clear-event') {
          const currentVal = localStorage.getItem('rewards-clear-event');
          // ## Checking if Iframe is from Ecom Web
            if (currentVal == 'REW') {
              if (window.top) {
                window.top.location.href = rewardsAppHomeUrl;
              }
            } else if (currentVal == 'EGG') {
              initRedirect();
              router.push('/');
            }
        }
      });
    }
  }, []);

  useEffect(() => {
    if(isInIframe && rewardsClearCookie){
      initRedirect();
    }
  }, []);

  // #. For delete clear-rewards cookie
  const initRedirect = async () => {
    await clearRewardsCookie();
  };

  // #. For clearing auth-related cookies for both EGG and rewards.
  const clearSessionCall = async () => {
    setTrigger(false);
    const type = isInIframe ? 'EGG' : 'REW';
    await clearSession(type);
    setTrigger(true);
    setisReloadPage(isReload);
  };

  useEffect(() => {
    if (isRewardsUser && trigger && !isReload) {
      groupGiftRewardsAuth()
        .then((response: any) => {
          const accessToken: string =
            response?.data?.rewardsRefreshToken?.accessToken;
          if (accessToken) {
            const userTokens = {
              isUserSignedIn: !!accessToken,
              isRewardsUser,
              AccessToken: accessToken,
              isGuestUser,
            };
            dispatch(setTokenInfo(userTokens));
          }
        })
        .catch((error) => {
          const userTokens = {
            isUserSignedIn: false,
            isRewardsUser: false,
            AccessToken: '',
            isGuestUser,
          };
          dispatch(setTokenInfo(userTokens));
          return error;
        });
    }
  }, [isRewardsUser, trigger, isReload]);

  useEffect(() => {
    if (!isUserSignedIn && guestTokens?.refereshToken) {
      FetchGuestAccessToken(guestTokens?.refereshToken)
        .then((data) => {
          const guestTokens = {
            GuestAccessToken: data?.data?.guestUserRefreshTokens?.accessToken,
            GuestRefreshToken: data?.data?.guestUserRefreshTokens?.refreshToken,
            isGuestUser: true,
          };
          dispatch(setGuestInfo(guestTokens));
        })
        .catch((error) => {
          const guestTokens = {};
          dispatch(setGuestInfo(guestTokens));
        });
    }
  }, []);

  useEffect(() => {
    if (isUserSignedIn) {
      setCookie(IS_GUEST_USER_COOKIE, 'false', 30);
    }
  }, [isUserSignedIn]);

  // #. Dispatch the user tokens
  useEffect(() => {
    if (!isRewardsUser && !isReload) {
      const userTokens = {
        AccessToken: accessToken,
        IdToken: idToken,
        RefreshToken: refreshToken,
        isUserSignedIn: isUserSignedIn,
        userAttributes: userAttributes,
        isGuestUser: isGuestUser,
        isRewardsUser,
      };

      dispatch(setTokenInfo(userTokens));
    }
  }, [refreshToken, accessToken, userAttributes, isUserSignedIn, isReload, isGuestUser]);

  // Return appropriate loading states based on conditions
  if (inIframe || isReloadPage ) {
    return <Loader />;
  }

  if (!trigger) {
    return useSkeletonLoader ? (
      <HomePageSkeleton isLoggedIn={isUserSignedIn} />
    ) : (
      <Loader />
    );
  }

  // If we're past all loading states, return null as authentication is complete
  return null;
};

export default AuthenticationProviderClient;
