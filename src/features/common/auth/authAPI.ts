import * as Sentry from '@sentry/nextjs';
import { USER_DETAILS_QUERY } from '../profile.query';
import { AUTH_TOKENS_QUERY } from '../common.query';
import { getClient } from '@/graphql/apolloClient';
import { store } from '@/redux/store';
import useCommonSlice from '../commonSlice';
import { getCookie } from '@/utils/getAndsetCookie';
import { IS_GUEST_USER_COOKIE } from '@/constants/common';

/**
 * @method getUserProfileDetails
 * @description fetch user details by passing access token
 * @param accessToken
 * @param locale
 * @returns user details query response object
 */
export const getUserProfileDetails = async (
  accessToken: string,
  locale: string = 'en'
) => {
  try {
    const input = {
      accessToken: accessToken,
    };
    let response = await getClient(locale).query({
      query: USER_DETAILS_QUERY,
      variables: {
        input,
      },
      context: {
        clientName: 'ecom-users',
        headers: {
          'accept-language': locale,
        },
      },
    });
    return response?.data?.userDetails || {};
  } catch (error) {
    console.log(error, 'error fetching user details');
  }
};

export const getTokenInfo = async (locale: string = 'en', token?: any, isClient?: boolean) => {
  try {
    let tokenInfo = await getClient(locale).query({
      query: AUTH_TOKENS_QUERY,
      context: {
        clientName: 'ecom-users',
        credentials: 'include',
        ...(!isClient && {
          headers: {
            'refresh-token': token,
          },
        })
      },
    });
    const {
      accessToken = '',
      idToken = '',
      refreshToken = null,
    } = tokenInfo?.data?.authTokens || {};

    if (accessToken && idToken) {
      return {
        accessToken: accessToken,
        idToken: idToken,
      };
    } else if (tokenInfo?.data?.errors?.length > 0) {
      console.error(tokenInfo?.data?.errors[0].message?.details);
      Sentry.captureMessage(
        `Error fetching token from token info ${JSON.stringify(
          tokenInfo?.data?.errors[0]
        )}`
      );

      return {
        accessToken: '',
        idToken: '',
        error: tokenInfo?.data?.errors[0].message?.details,
      };
    } else {
      console.log('Error fetching token', tokenInfo);
      Sentry.captureMessage(
        `Error fetching token ${JSON.stringify(tokenInfo)}`
      );
    }
    return {
      accessToken: '',
      idToken: '',
    };
  } catch (error) {
    console.log('Error fetching token', error);
    Sentry.captureMessage(`Error fetching token ${JSON.stringify(error)}`);
    return {
      accessToken: '',
      idToken: '',
    };
  }
};

export const getAuthInfo = async (
  locale: string = 'en',
  refreshToken?: string,
  isClient?: boolean,
) => {
  try {
    const tokens = await getTokenInfo(locale, refreshToken, isClient);
    const idToken = tokens?.idToken ?? '';
    const accessToken = tokens?.accessToken ?? '';
    let displayName = '',
      isUserSignedIn = false,
      userAttributes = null;
    if (accessToken != '') {
      isUserSignedIn = true;
      userAttributes = await getUserProfileDetails(accessToken);
      displayName = userAttributes?.name;
    }
    return {
      idToken,
      accessToken,
      isUserSignedIn,
      displayName,
      userAttributes,
    };
  } catch (error) {
    console.log('error is ', error);
    return {
      idToken: '',
      accessToken: '',
      isUserSignedIn: false,
      displayName: '',
      userAttributes: {},
    };
  }
};

// #. Next api call for clear rewards-clear cookie
export const clearRewardsCookie = async () => {
  try {
    const res = await fetch('/api/clear-rewards', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    const data = await res.json();
  } catch (error) {
    console.error('Error:', error);
  }
};

// #. Next api call for clearing auth-related cookies for both EGG and rewards.
export const clearSession = async (type: string) => {
  try {
    const response = await fetch('/api/clear-session/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ type }),
    });
    
    localStorage.setItem('rewards-clear-event', type);
    const data = await response.json();
  } catch (error) {
    console.error('Error:', error);
  }
};
// # Handle token rotation in EGG flow
export const groupGiftTokenInfo = async() => {
  // Get the Guest user cookie
  const isGuestLoginCookie: any = getCookie(IS_GUEST_USER_COOKIE);
  const isGuestUser = isGuestLoginCookie === 'true'
  // Get the previous refreshToken
  const previousTokenInfo = store.getState().common.tokenInfo;
  const { setTokenInfo } = useCommonSlice();
  const authResponse = await getAuthInfo("en", "",true);
  const {
    idToken,
    accessToken,
    isUserSignedIn,
    userAttributes,
  } = authResponse;

  const userTokens = {
    ...previousTokenInfo,
    AccessToken: accessToken,
    IdToken: idToken,
    isGuestUser,
    isUserSignedIn: isUserSignedIn,
    userAttributes: userAttributes,
    isRewardsUser : false,
  };
  store.dispatch(setTokenInfo(userTokens));
  return accessToken;
}
