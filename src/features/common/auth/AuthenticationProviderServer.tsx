import { IS_GUEST_USER_COOKIE, LOCALE_REGION_COOKIE } from '@/constants/common';
import { getUserInfo } from '@/utils/authInfo';
import getCookie from '@/utils/getCookie';
import AuthenticationProviderClient from './AuthenticationProviderClient';

/**
 * @method AuthenticationProviderServer
 * @returns AuthenticationProviderClient component with server-fetched data
 */
const AuthenticationProviderServer = async ({
  children,
  useSkeletonLoader = false,
}: {
  children: React.ReactNode;
  useSkeletonLoader?: boolean;
}) => {
  // Get token info API (server side) call
  const userInfo = await getUserInfo();
  const guestRefereshTokenCookie: any = getCookie('GROUPGIFT_REFRESH_TOKEN');
  const isGuestLoginCookie: any = getCookie(IS_GUEST_USER_COOKIE);
  const cookieLocale: any = getCookie(LOCALE_REGION_COOKIE);

  const guestTokens = {
    refereshToken: guestRefereshTokenCookie?.value || '',
    isGuestLoggedIn: isGuestLoginCookie?.value || '',
  };

  // Always render children for SEO, and let the client component handle authentication
  return (
    <>
      {children}

      {/* Client-side authentication handling */}
      <AuthenticationProviderClient
        userInfo={userInfo}
        guestTokens={guestTokens}
        useSkeletonLoader={useSkeletonLoader}
        cookieLocale={cookieLocale}
      />
    </>
  );
};

export default AuthenticationProviderServer;
