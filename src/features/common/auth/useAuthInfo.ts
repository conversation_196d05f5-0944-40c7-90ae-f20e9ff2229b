import { useState, useEffect } from 'react';
import { getAuthInfo } from './authAPI';

const useAuthInfo = (userCookies: any) => {
  const [authInfo, setAuthInfo] = useState({
    refreshToken: '',
    idToken: '',
    accessToken: '',
    isUserSignedIn: false,
    displayName: '',
    userAttributes: '',
    isGuestUser: false,
  });

  useEffect(() => {
    const fetchData = async () => {
      const refreshToken = userCookies?.refreshTokenCookie?.value ?? '';
      let isGuestUser = false;

      const guestUserCookie = userCookies?.GUEST_USER_SESSION_COOKIE;
      if (guestUserCookie) {
        isGuestUser = true;
      }

      if (refreshToken) {
        const {
          idToken,
          accessToken,
          isUserSignedIn,
          displayName,
          userAttributes,
        } = await getAuthInfo("en",String(refreshToken), false);

        setAuthInfo({
          refreshToken,
          idToken,
          accessToken,
          isUserSignedIn,
          displayName,
          userAttributes,
          isGuestUser,
        });
      } else {
        setAuthInfo({
          refreshToken: '',
          idToken: '',
          accessToken: '',
          isUserSignedIn: false,
          displayName: '',
          userAttributes: '',
          isGuestUser,
        });
      }
    };

    fetchData();
  }, [userCookies]);

  return authInfo;
};

export default useAuthInfo;
