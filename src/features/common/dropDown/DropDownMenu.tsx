import React from 'react';
import styles from './DropDownMenu.module.scss';

/**
 * @method DropDownMenu
 * @description Dropdown component
 * @returns {JSX.Element}
 */
const DropDownMenu = ({
  children,
  width,
  dropDown,
  className = "",
  hasMultipleLanguages = false,
  setDropDown,
}: any):JSX.Element => {
  return (
    <div
      onMouseEnter={() => {
        setDropDown && setDropDown(true);
      }}
      onMouseLeave={() => {
        setDropDown && setDropDown(false);
      }}
      className={`${styles['drop-down-menu']} ${
        dropDown ? styles['drop-down-menu--active'] : ''
      } ${hasMultipleLanguages ? styles['drop-down-menu--expanded'] : ''
      } ${className} ${styles[className]}`}
      style={width ? { width: width } : {}}
      data-testid="dropDownMenu"
    >
      {children}
    </div>
  );
}

export default DropDownMenu;