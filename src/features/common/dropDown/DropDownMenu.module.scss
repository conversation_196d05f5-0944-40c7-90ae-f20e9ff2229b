@import '@/styles/abstracts/abstracts';

.drop-down-menu {
	border-radius: 12px;
	position: absolute;
	left: 100px;
	transform: translateX(-50%);
	top: 100%;
	overflow-y: hidden;
	overflow-x: hidden;
	min-width: 16px;
	outline: 0;
	transition: all 300ms ease-in;
	opacity: 0;
	height: 0;
	padding: 10px 0 0 0;
	z-index: 999999;

	&--active {
		height: fit-content;
		opacity: 1;
	}

	&--expanded {
		left: 130%;
	}

	@include rtl-styles {
		left: -8%;
	}
}

.drop-down-user-info{
	right: 30% !important;
	left: auto;
	transform: translateX(0%) !important;

	@include rtl-styles {
		left: 30%;
		right: auto !important;
	}
}
