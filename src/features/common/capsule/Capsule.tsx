'use client';
import React from 'react';
import styles from './Capsule.module.scss';
import { useTranslation } from 'react-i18next';
import { HAS_WINDOWS_REF } from '@/utils/hasWindowRef';
import SvgIcon from '../svgIcon/SvgIcon';

export default function Capsule({ data }: any) {
  const { t } = useTranslation();

  // #. Check payment success or failer page
  const isPaymentVerifyPage =
    HAS_WINDOWS_REF &&
    (window.location.pathname.includes('payment-failed') ||
      window.location.pathname.includes('payment-success'));

  return (
    <>
      {data?.groupGiftDetail?.isOrganizer && !isPaymentVerifyPage && (
        <div className={styles['capsule']}>
          <SvgIcon icon="eye" width="16" height="16" />
          {t('organizerView')}
        </div>
      )}
    </>
  );
}
