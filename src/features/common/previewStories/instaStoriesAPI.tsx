import { PREVIEW_TYPES } from '@/features/gifts/giftsOpen/constants/constants';
import S3Bucket from '@/utils/s3Bucket';
import isAbsoluteURL from '@/utils/isAbsoluteURL';
import { PERSONALISATION_TYPE } from '@/constants/common';

interface IPreviewStories {
  greetingsPath: string | undefined;
  imagePath?: string;
  videoPath?: string;
  giftMessage?: {
    backgroundColor: string;
    fontFamily: string;
    fontSize: string;
    message: string;
    messagePropEl: HTMLElement;
  };
}

const useInstaStoriesAPI = () => {
  /**
   * @method updateGiftMessageStory
   * @description Update gift message story state
   * @param message
   */
  const updateGiftMessageStory = (giftMessageProps: any, messageEl: any) => {
    if (!messageEl) {
      return [];
    }

    const { backgroundColor, fontFamily, fontSize, message }: any =
      giftMessageProps;

    if (message && fontSize) {
      // #. Set the message story into desired format
      const content = {
        duration: 5000,
        content: message,
        fontSize,
        style: {
          fontFamily: fontFamily,
          fontSize: `${fontSize}px`,
          backgroundColor: backgroundColor,
          color: '#fff',
          borderRadius: '12px',
          wordBreak: 'break-word',
          height: '537px',
        },
        type: PERSONALISATION_TYPE.TEXT,
        __type: PREVIEW_TYPES.message,
      };
      return [content];
    } else {
      return [];
    }
  };

  /**
   * @method updateGiftMessageStory
   * @description Update gift message story state
   * @param message
   */
  const updateVideoStory = (video?: string) => {
    if (video) {
      // #. Set the message story into desired format
      const content = {
        url: video,
        type: PERSONALISATION_TYPE.VIDEO,
        __type: PREVIEW_TYPES.video,
      };

      return [content];
    } else {
      return [];
    }
  };

  /**
   * @method updateGreetingStory
   * @description Update gift message story state
   * @param message
   */
  const updateGreetingStory = (imagePath?: string) => {
    if (imagePath) {
      // #. Set the message story into desired format
      const content = {
        url: imagePath,
        __type: PREVIEW_TYPES.greeting,
        type: PERSONALISATION_TYPE.IMAGE,
      };

      return [content];
    } else {
      return [];
    }
  };

  /**
   * @method updatePhotoStory
   * @description Update gift message story state
   * @param message
   */
  const updatePhotoStory = (imagePath?: string) => {
    if (imagePath) {
      // #. Set the message story into desired format
      const content = {
        url: imagePath,
        __type: PREVIEW_TYPES.photo,
        type: PERSONALISATION_TYPE.IMAGE,
      };

      return [content];
    } else {
      return [];
    }
  };

  /**
   * @method getStories
   * @description Return stories based on the current update
   * @param message
   */
  const getStories = async ({
    greetingsPath,
    videoPath,
    imagePath,
    giftMessage,
  }: IPreviewStories) => {
    return [
      ...updateGreetingStory(greetingsPath),
      ...updateVideoStory(videoPath),
      ...updatePhotoStory(imagePath),
      ...updateGiftMessageStory(giftMessage, giftMessage?.messagePropEl),
    ];
  };

  /**
   * @method getPersonalizedUploadedItemsURL
   * @description Get image url by image key from S3 storage
   * @param {key : string} might be absolute URL or null or S3 bucket item key
   */
  const getPersonalizedUploadedItemsURL = async (key: string) => {
    if (!key) {
      return null;
    }

    if (isAbsoluteURL(key)) {
      return key;
    }

    const signedURL = await S3Bucket.get(key, {
      level: 'public',
      expires: 86400,
    });

    return signedURL;
  };

  return {
    getStories,
    getPersonalizedUploadedItemsURL,
  };
};

export default useInstaStoriesAPI;
