@import '@/styles/abstracts/abstracts';

.preview-stories {
  &__container {
    position: relative;
    height: 610px;

    @media (max-width: ($md + 40)) {
      height: 490px;
    }
  }

  &__title {
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    @include font-size(14);
    color: $dark-purple;
    font-weight: 500;
  }

  &__phone-body {
    position: absolute;
    left: 70px;
    width: 300px;

    @media (max-width: ($md + 40)) {
      left: 0;
      top: 10%;
      width: 90% !important;
    }

    @include rtl-styles {
      right: 22px;
    }
  }

  &__stories {
    > :last-child {
      > div:first-child {
        div {
          background-color: $white !important;
          > div {
            background-color: $barney-purple !important;
          }
        }
      }
    }

    > div {
      background: $pale-grey-bg !important;

      > div:nth-child(2) {
        align-items: flex-start !important;
        margin-top: 30px;

        div {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;

          img {
            border-radius: 12px;
            width: calc(100% - 15px) !important;
          }
        }
      }
    }

    position: absolute;
    top: 56px;
    left: 93px;
    direction: ltr;

    @media (max-width: ($md + 40)) {
      left: 14px;
      top: 92px;
    }

    @include rtl-styles {
      left: 196px;

      @media (max-width: ($lg + 40)) {
        left: 150px;
      }

      @media (max-width: ($md + 40)) {
        left: 36px;
      }
    }
  }

  &__preview-button {
    position: absolute;
    right: 25px;
    top: 50%;

    @media (max-width: ($md + 40)) {
      right: -50px;
      top: 40%;
    }

    @media (max-width: ($lg + 40)) {
      right: 0;
    }

    @include rtl-styles {
      left: -50%;

      @media (max-width: ($lg + 40)) {
        left: -73%;
      }

      @media (max-width: ($md + 40)) {
        bottom: 78px;
        width: 100%;
        left: 17px;
      }
    }

    a {
      @include font-size(12);

      color: $barney-purple;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 5px;
      cursor: pointer;

      @media (max-width: ($md + 40)) {
        @include font-size(13);
      }

      @include rtl-styles {
        margin-right: 20px;
      }

      img {
        width: 34px;
        height: 34px;
        display: block;

        @include rtl-styles {
          transform: rotate(180deg);
        }
      }
    }
  }

  &__message-display {
    color: $white;
    height: calc(100% - 10px);
    overflow: hidden;
    width: 100%;
    padding: 15px 10px 25px;
    text-align: center;
    border-radius: 20px;

    span {
      display: flex;
      align-items: center;
      height: calc(100% - 15px);
      justify-content: center !important;
      word-break: break-word;
    }
  }

  &__message-see-more {
    @include font-size(12);

    font-weight: 500;
    text-align: center;
    width: 100%;
    bottom: 10px;
    position: relative;
    color: $white;
    cursor: pointer;
    margin: 0 0 3px;
  }

  &__see-all-message {
    overflow: scroll;
    height: calc(100% - 25px);
    margin: 15px 10px 5px;
    padding: 5px 0;
    border-radius: 20px;

    > div {
      margin: 0;
      padding: 0;

      > span {
        align-items: flex-start;
        width: 100%;
        display: block;
      }
    }
  }

  &__see-more-close {
    @include font-size(12);

    font-weight: 500;
    color: $white;
    text-align: center;
    cursor: pointer;
    width: 100%;
    position: absolute;
    bottom: 7px;
    margin: 0 0 3px;
  }
}
