import { PLATFORM_TYPE } from '@/constants/common';
import { gql, useQuery } from '@apollo/client';

// for showing cart count in the headers only - GG

interface BasicCartData {
  id: string;
  totalQuantity: number;
}

const CART_BASIC_SUMMARY_QUERY = gql`
  query {
    cart {
      id
      totalQuantity
    }
  }
`;

export const FetchBasicCartData: any = (regionCode: string, token: any) => {
  const headers: any = {
    'access-locale': regionCode,
    'app-platform': PLATFORM_TYPE.WEB.toLowerCase(),
    'Authorization': token,
  };

  const {
    loading: basicCartDataLoading,
    error: basicCartDataError,
    data: basicCartDataData,
  } = useQuery<BasicCartData>(CART_BASIC_SUMMARY_QUERY, {
    context: {
      clientName: 'ecomcart',
      headers,
      credentials: 'include',
    },
    skip: !token,
  });

  return {
    basicCartDataLoading,
    basicCartDataError,
    basicCartDataData,
  };
};
