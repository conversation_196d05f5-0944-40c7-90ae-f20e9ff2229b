'use client';

import styles from './HomeFooter.module.scss';
import useHomeAPI from '@/features/home/<USER>';

const FooterPaymentPartners = () => {
  // getting query data
  const { usePaymentPartners } = useHomeAPI();
  const { data } = usePaymentPartners();
  const paymentPartners = data?.footer?.paymentPartners;

  return (
    <>
      <div className={styles['payment-cards']}>
        {paymentPartners?.edges
          ?.filter(
            ({ node: { code } }: { node: { code: string } }) =>
              code !== 'PCIDSS'
          )
          ?.map(
            (
              {
                node: { logo, code },
              }: { node: { logo: string; code: string } },
              index: number
            ) => (
              <img
                src={logo}
                alt={code}
                id={`e2eTestingFooter${code}`}
                data-testid="paymentCardsLogo"
                key={code}
              />
            )
          )}
           {paymentPartners?.edges
        ?.filter(
          ({ node: { code } }: { node: { code: string } }) => code === 'PCIDSS'
        )
        ?.map(
          (
            { node: { logo, code } }: { node: { logo: string; code: string } },
            index: number
          ) => (
            <div className={styles['payment-solution']} key={code}>
              <img
                src={logo}
                alt={code}
                id={`e2eTestingFooter${code}`}
                data-testid="paymentCardsPCIDSSLogo"
              />
            </div>
          )
        )}
      </div>

     
    </>
  );
};

export default FooterPaymentPartners;