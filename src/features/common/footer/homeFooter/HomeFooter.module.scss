@import '@/styles/abstracts/abstracts';

.footer {
  background-color: $white;
  border-radius: 0 0 24px 24px;
  position: relative;

  &__row {
    &--first {
      padding: 32px 0 30px;
      display: grid;
      gap: 110px;
      grid-template-columns: 1.70fr 0.97fr 0.97fr auto;

      @include rtl-styles {
        gap: 80px;
        grid-template-columns: 1.7fr 0.9fr 0.8fr auto;
      }

      @media (max-width: ($lg + 40)) {
        gap: 60px;
        grid-template-columns: 1.6fr 1.1fr 1.1fr auto;

        @include rtl-styles {
          gap: 40px;
          grid-template-columns: 2.6fr 2.1fr 1.7fr auto;
        }
      }

      @media (max-width: ($md + 40)) {
        display: flex;
        flex-wrap: wrap;
        gap: 0;
      }
    }

    &--second {
      display: flex;
      justify-content: space-between;
      height: 88px;
      align-items: center;
    }

    h5 {
      margin: 0 0 24px 0;
      font-family: var(--font-bricolage); font-optical-sizing: none;
      font-size: 18px;
      font-style: normal;
      font-weight: 800;
      line-height: 16px;
      letter-spacing: -0.09px;
      color: $dark-charcoal;
    }
  }
  &__robbon {
    position: absolute;
    top: 0;
  }

  &__tech-GiftCard-Wrapper {
    display: flex;

    a {
      flex: 1;
    }
  }
  &__dev-text,
  &__solution-text {
    background: $medium-light-grey;
    border-radius: 0px 24px 24px 0px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;
    height: 150px;
    h4 {
      font-size: 32px;
      font-weight: 800;
      letter-spacing: -0.16px;
      color: $dark-charcoal;
    }
    img{
      width: 40px;
      @include rtl-styles{
        transform: rotate(270deg);
      }
    }

    @include rtl-styles{
      border-radius: 24px 0px 0px 24px;
    }
  }
  &__solution-text {
    border-radius: 24px 0 0 24px;
    background: rgba(153, 198, 255, 0.3);
    
    @include rtl-styles{
      border-radius: 0px 24px 24px 0;
    }
  }

  &__label-container{
    display: flex;
    position: relative;
    margin-top: 20px;
    gap: 16px;

    &--label{
      color: $dark-charcoal;
      text-align: right;
      /* Heading/H1 */
      font-family: var(--font-bricolage); font-optical-sizing: none;
      font-size: 32px;
      font-style: normal;
      font-weight: 800;
      line-height: 40px; /* 125% */
      letter-spacing: -0.16px;
    }

    &--caption{
      position: absolute;
      left: 0px;
      top: -15px;
      color: $dark-charcoal;
      text-align: right;
      /* Heading/H3 */
      font-family: var(--font-bricolage); font-optical-sizing: none;
      font-size: 18px;
      font-style: normal;
      font-weight: 800;
      line-height: 16px; /* 88.889% */
      letter-spacing: -0.09px;

      @include rtl-styles{
        right: 0px;
      }
    }
  }

}


.footer-logo {
  width: 53px;
  margin-top: 48px;
}


.first-row {


  &__column {
    &--company-data {
      // display: grid;
      // grid-template-columns: 1fr 1fr;

      @media (max-width: ($lg + 40)) {
        grid-template-columns: 1.6fr 0.4fr;
      }

      @media (max-width: ($md + 40)) {
        flex: 250px;
        width: 250px;
        order: 4;
      }

      p {
        color: $dark-charcoal;
        max-width: 300px;
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: 24px;
        letter-spacing: -0.16px;
      }

      &--social {
        display: flex;
        margin: 43.5px 0 0 0;

        img {
          width: 40px;
          height: 40px;
          margin-right: 16px;

          @include rtl-styles{
            margin-left: 16px;
            margin-right: unset;
          }
        }
      }
    }

    &--nav01 {
      @media (max-width: ($md + 40)) {
        flex: 132px;
        width: 132px;
        order: 1;
      }
    }

    &--nav02 {
      @media (max-width: ($md + 40)) {
        flex: 132px;
        width: 132px;
        order: 2;
      }
    }

    &--contact {
      @media (max-width: ($md + 40)) {
        flex: 132px;
        width: 132px;
        order: 3;
      }
    }

    &--store {
      >p {
        margin: 0 5px 5px 0;
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: 24px;
        letter-spacing: -0.16px;
        color: $dark-charcoal;
      }
      p {
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: 24px;
        letter-spacing: -0.16px;

      }

      @media (max-width: ($md + 40)) {
        margin-top: 4px;
        flex: 237px;
        order: 5;
        display: flex;
        flex-direction: column;

        h5 {
          display: none;
        }
      }

      &__help-center {
        display: flex;
        margin-bottom: 28px;

        >p {
          margin: 0;
          font-size: 16px;
          font-style: normal;
          font-weight: 500;
          line-height: 24px;
          letter-spacing: -0.16px;
          color: $dark-charcoal;
        }

        >a {
          color: $barney-purple;
          font-size: 16px;
          font-style: normal;
          font-weight: 600;
          line-height: 24px;
          letter-spacing: -0.16px;
          margin-left: 5px;
        }
      }

      &__app-download {
        display: flex;
        padding: 8px 16px 8px 8px;
        align-items: center;
        gap: 16px;
        align-self: stretch;
        border-radius: 12px;
        background: $medium-light-grey;
        height: 80px;
        margin-top: 25px;
        width: 243px;

        img {
          width: 57px;
        }

        p {
          color: #0E0F0C;
      
          font-size: 16px;
          font-style: normal;
          font-weight: 700;
          line-height: 16px;
          letter-spacing: -0.16px;
          margin: 8px 0;
        }
      }
    }
  }
}

.payment-cards {
  display: flex;
  padding: 0px ;
  max-width: 300px;
  gap: 20px;
  flex-wrap: wrap;

  @media (max-width: ($md + 40)) {
    padding-top: 17px;
  }

  img {
    width: 46.875px;
    height: 18px;
    margin-right: 0px;
  }
}

.payment-solution {
  img {
    max-width: 104px;
  }
}

.footer-nav {

  a,
  span {
    display: block;
    margin: 20px 0;
    color: $dark-charcoal;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
    letter-spacing: -0.16px;

    &:hover {
      color: $dark-charcoal;
      font-weight: 500;
    }
  }
}

.link-mail {
  display: flex;
  align-items: center;
  gap: 10px;

  i {
    color: $cool-grey;

    @include font-size(12);
  }
}

.links-store {
  margin-bottom: 18px;
  margin-top: 23px;
  display: flex;
  flex-direction: column;

  @media (max-width: ($md + 40)) {
    order: 2;
    flex-direction: row;
    margin-top: 15px;
    margin-right: 20px;
    justify-content: flex-end;
  }

  img {
    max-height: 54px;
    margin-bottom: 20px;

    @media (max-width: ($md + 40)) {
      height: 33px;
      margin-left: 15px;
    }
  }
}

.contact-row {
  font-weight: 500;
  color: $dark-grey;
  line-height: 21px;
  margin: 30px 0 40px;

  i {
    margin-right: 8px;

    @include rtl-styles {
      margin-left: 8px;
      margin-right: 0;
    }

    img {
      height: 12px;
    }
  }

  span {
    display: block;
    @include font-size(10);

    line-height: 10px;
    color: $warm-grey;
    position: relative;
    top: 1px;
    font-weight: 500;
  }
}

.links-social-media {
  display: flex;
  align-items: center;
  height: 45px;

  a {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    height: 26px;

    img {
      margin-right: 16px !important;
    }

    @include rtl-styles {
      img {
        margin-right: 0 !important;
        margin-left: 16px;
      }
    }

    &:last-child {
      margin-right: 0;
    }

    i {
      color: $warm-grey;
    }
  }
}

.second-row {
  @include font-size(14);

  color: $dark-charcoal;
  font-weight: 500;
  font-style: normal;
  line-height: 24px;
  letter-spacing: -0.14px;

  @media (max-width: ($md + 40)) {
    flex-direction: column;
  }

  &__copyright {
    @media (max-width: ($md + 40)) {
      order: 2;
      margin-top: 23px;
    }
  }

  &__copyright-container{
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    gap: 32px;
  }

  &__available-regions {
    cursor: pointer;
    display: flex;

    @media (max-width: ($md + 40)) {
      order: 1;
    }

    .sep {
      font-weight: 200;
      display: inline-block;
      padding: 0 3px;
    }
  }
}

.hiring-align{
  display: flex !important;
  gap: 8px !important;
  align-items: center;
}

