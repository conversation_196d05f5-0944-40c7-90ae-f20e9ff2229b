'use client';

import React from 'react';
import HomeFooter from './homeFooter/HomeFooter';
import InnerFooter from './innerFooter/InnerFooter';
import { FOOTER_TYPE } from '@/constants/common';

/**
 * @method Footer
 * @description Footer component (Based on type)
 * @returns {JSX.Element}
 */

interface FooterProps {
  type?: string;
  footerData?: any;
  paymentPartnersData?: any;
}

const Footer = ({
  type,
  footerData,
  paymentPartnersData,
}: FooterProps): JSX.Element => {
  return type === FOOTER_TYPE.HOME ? (
    <HomeFooter footerData={footerData} paymentPartners={paymentPartnersData} />
  ) : (
    <InnerFooter footerData={footerData} />
  );
};

export default Footer;
