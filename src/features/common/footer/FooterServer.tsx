import { FOOTER_QUERY, PAYMENT_PARTNERS_QUERY } from '@/features/common/common.query';
import { LOCALE_REGION_COOKIE, PLATFORM_TYPE } from '@/constants/common';
import { getClient } from '@/graphql/apolloClient';
import Footer from './Footer';
import getCookie from '@/utils/getCookie';
import { cache } from 'react';

/**
 * @method fetchFooterData
 * @description Fetch footer data from server
 * @returns footer data
 */
// Use React cache to prevent duplicate fetches during server rendering
const fetchFooterData = cache(async (locale: string, region: string) => {
  try {
    const response = await getClient(locale).query({
      query: FOOTER_QUERY,
      variables: {
        platformType_Code: PLATFORM_TYPE.WEB,
        country_Code: region ? region.toUpperCase() : 'AE',
      },
      context: {
        clientName: 'webstore-with-cdn',
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching footer data:', error);
    return null;
  }
});

/**
 * @method fetchPaymentPartnersData
 * @description Fetch payment partners data from server
 * @returns payment partners data
 */
// Use React cache to prevent duplicate fetches during server rendering
const fetchPaymentPartnersData = cache(async (locale: string, region: string) => {
  try {
    const response = await getClient(locale).query({
      query: PAYMENT_PARTNERS_QUERY,
      variables: {
        platformType_Code: PLATFORM_TYPE.WEB,
        country_Code: region ? region.toUpperCase() : 'AE',
      },
      context: {
        clientName: 'webstore-with-cdn',
      },
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching payment partners data:', error);
    return null;
  }
});

/**
 * @method FooterServer
 * @returns Footer component with server-fetched data
 */
const FooterServer = async ({ type }: { type?: string }) => {
  // Get locale and region from cookie
  const localeRegionCookie: any = getCookie(LOCALE_REGION_COOKIE);
  const localeRegion = localeRegionCookie?.value || 'en-ae';
  const region = localeRegion.split('-')[1];
  const locale = localeRegion.split('-')[0];

  // Fetch data on the server with Promise.all for parallel fetching
  const [footerData, paymentPartnersData] = await Promise.all([
    fetchFooterData(locale, region),
    fetchPaymentPartnersData(locale, region)
  ]);

  // Pass data to client component
  return <Footer
    type={type}
    footerData={footerData?.footer}
    paymentPartnersData={paymentPartnersData?.footer?.paymentPartners}
  />;
};

export default FooterServer;
