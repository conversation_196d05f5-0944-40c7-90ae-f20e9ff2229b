import React from 'react';
import Link from 'next/link';
import styles from './InnerFooter.module.scss';
import { ecomRedirectionBaseUrl } from '@/constants/apiEndPoints';
import getRegionAndLocale from '@/utils/getRegionAndLocale';
import { useTranslation } from 'react-i18next';
import CustomLink from '../../customLink/CustomLink';

/**
 * @method InnerFooter
 * @description Inner pages footer component
 * @returns {JSX.Element}
 */
const InnerFooter = ({ footerData }: { footerData: any }): JSX.Element => {
  const { locale, region }: any = getRegionAndLocale();
  const { copyrightNotice, subMenu } = footerData || {};
  const privacyPolicy = subMenu?.[1]?.children?.edges?.[0]?.node;
  const termsOfUse = subMenu?.[1]?.children?.edges?.[1]?.node;
  const footerStores = footerData?.footerStores;
  const { t } = useTranslation();

  const redirectUrl = (itemUrl: string) =>
    itemUrl.indexOf('http') !== -1
      ? itemUrl
      : `${ecomRedirectionBaseUrl}${locale}-${region}${itemUrl}`;

  return (
    <div className={styles['inner-footer']}>
      <span>{copyrightNotice}</span>
      &nbsp;-&nbsp;
      {privacyPolicy && (
        <>
          <CustomLink hrefLink={redirectUrl(privacyPolicy.itemUrl)} prefetch={false}>
            {privacyPolicy.itemLabel}
          </CustomLink>
          <span className={styles['separator']}>&nbsp;|&nbsp;</span>
        </>
      )}
      {termsOfUse && (
        <CustomLink hrefLink={redirectUrl(termsOfUse.itemUrl)} prefetch={false}>
          {termsOfUse.itemLabel}
        </CustomLink>
      )}
    </div>
  );
};

export default InnerFooter;
