@import '@/styles/abstracts/abstracts';

.inner-footer {
  height: 90px;
  display: flex;
  justify-content: center;
  align-items: end;
  max-width: 1280px;
  margin: 0 auto;
  border-top: 1px solid $white-smoke;
  padding-bottom: 30px;

  & > a {
    color: $semi-dark-purple;
    font-weight: 500;
    line-height: 18px;
    letter-spacing: -0.14px;
  }

  & > span {
    font-size: rem(14);
    color: $dark-charcoal;
    font-weight: 500;
  }

  .separator {
    color: $semi-dark-purple;
  }
}
.available-regions {
  color: $dark-charcoal;
  text-align: right;
  font-size: rem(14);
  font-weight: 500;
  line-height: 24px;
  letter-spacing: -0.14px;
}
.region-info {
  cursor: pointer;

  &:hover {
    color: #b800c4 !important;
  }
}