import React, { useEffect, useState } from 'react';
import styles from './ReceiverAndDelivery.module.scss';
import GiftDeliveryScheduler from './GiftDeliveryScheduler';
import SvgIcon from '@/features/common/svgIcon/SvgIcon';
import { useTranslation } from 'react-i18next';
import {
  AdditionalSettings,
} from '../interfaces/startGroupGift.Interface';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import useStartGroupGiftSlice from '../startGroupGiftSlice';
import { useSearchParams } from 'next/navigation';
import { findCountryByCode } from '@/utils/findCountryByCode';
import useCommonSlice from '@/features/common/commonSlice';

/**
 * @method GiftDelivery
 * @description
 * @returns {JSX.Element}
 */
const GiftDelivery = (): JSX.Element => {
  // translations
  const { t } = useTranslation();
  const [selectDate, setSelectDate] = useState<boolean>(false);

  const {
    setScheduledInfo,
    getAdditionalSettings,
    getScheduledInfo,
    getSelectedOccasionAndBrand,
    getGiftCreatedStore,
  } = useStartGroupGiftSlice();

  const { getStores } = useCommonSlice();

  const { date, time, timeZone }: any = useAppSelector(getScheduledInfo);
  const additionalSettings: AdditionalSettings = useAppSelector(
    getAdditionalSettings
  );
  const { brand }: any = useAppSelector(getSelectedOccasionAndBrand);
  const dispatch = useAppDispatch();
  const searchParams = useSearchParams();
  const isEdit = searchParams.get('edit');
  const isRewards = searchParams.get('embedded');
  const giftCreatedStore: any = useAppSelector(getGiftCreatedStore);
  const storesData: any = useAppSelector(getStores);

  // #.Get currency iso code
    const matchedCountry =
      giftCreatedStore?.isoCode?.toLowerCase() &&
      findCountryByCode(
        giftCreatedStore?.isoCode?.toLowerCase(),
        storesData?.stores?.edges
      );
  const currency = isEdit 
  ? (isRewards ? giftCreatedStore?.currency?.isoCode : matchedCountry?.country.currency.code) 
  : brand?.currency?.isoCode;
  const brandMaxAmount = brand?.denominationRange?.maxAmount;

  /**
   * @method handleSelectDate
   * @description handle the visibility of GiftDeliveryScheduler component.
   */
  const handleSelectDate = () => {
    setSelectDate(true);
  };

  /**
   * @method handleClearDate
   * @description clear the Date, Time and Timzone.
   */
  const handleClearDate = () => {
    dispatch(
      setScheduledInfo({
        date: '',
        time: '',
        timeZone: '',
      })
    );
    setSelectDate(false);
  };

  useEffect(() => {
    if (date && time && timeZone) {
      setSelectDate(true);
    }
  }, []);

  return (
    <>
      <div className={styles['delivery-section']}>
        <div
          className={` ${styles['delivery-section__container']} ${
            selectDate ? styles['delivery-section__container--expand'] : ''
          }`}
        >
          <div
            className={`${styles['delivery-header']} ${
              selectDate ? styles['delivery-header--expand'] : ''
            }`}
          >
            <div className={styles['delivery-header__left']}>
              <SvgIcon icon="delivery-icon" width="24" height="24" />
              {selectDate ? (
                <p>
                  {t('deliver')} {/* &#x2022; */}
                </p>
              ) : (
                <p>
                  {t('deliver')} {/* <span>{t('optional')}</span> */}
                </p>
              )}
            </div>
            <div
              className={`${styles['delivery-header__right']} ${
                selectDate ? styles['delivery-header__right--expand'] : ''
              }`}
            >
              <div className={styles['selector']} onClick={handleSelectDate}>
                <span>{t('selectDate')}</span>
              </div>
            </div>
            {selectDate && (
              <div className={styles['delivery-header__clear']}>
                <a onClick={handleClearDate}>{t('clear')}</a>
              </div>
            )}
          </div>
          {selectDate && <GiftDeliveryScheduler />}
        </div>
        <div className={styles['delivery-section__message']}>
          {/* <div className={styles['delivery-section__message-info-gift']}>
          <SvgIcon icon="idea" width="16" height="16" />
          {additionalSettings?.hasCollectionTarget ? (
          <ul>
            <li>{t('deliveryInfo1')}</li>
            {<li>{t('deliveryInfo2')}</li>}
          </ul>
          ) : (
            <span>{t('deliveryInfo1')}</span>
          )}
        </div> */}
        </div>
      </div>
      <div className={styles['delivery-section__message-info-gift']}>
        {additionalSettings?.hasCollectionTarget ? (
          <ul>
            <li>
              <span></span>
              {t('deliveryInfo1')}
            </li>
            <li>
              <span></span>
              {t('deliveryInfo2')}
            </li>
            <li>
              <span></span>
              {t('deliveryInfo3', {
                amount: `${brandMaxAmount} ${currency}`,
              })}
            </li>
          </ul>
        ) : (
          <span>{t('deliveryInfo1')}</span>
        )}
      </div>
    </>
  );
};

export default GiftDelivery;
