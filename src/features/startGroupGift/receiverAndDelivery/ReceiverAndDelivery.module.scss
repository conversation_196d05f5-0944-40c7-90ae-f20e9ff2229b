@import '@/styles/abstracts/abstracts';

.receiver-delivery {
  width: 548px;
  margin-left: rem(21);
  margin-top: rem(32);
  @include rtl-styles {
    margin-left: 0;
    margin-right: rem(29);
  }
  .receiver-section {
    .icons{
      width: 24px !important;
      height: 24px !important;
    }
    .sms-icon{
      @include rtl-styles{
        margin-right: 8px;
      }
    }
    &__details {
      padding: 8px;
      border-radius: 12px;
      background-color: $medium-light-grey;
      margin-bottom: rem(16);
      
      &-form {
        display: flex;
        flex-direction: column;
        gap: 8px;
        align-items: start;
        height: 100%;
        .phone-code { 
          display: flex;
          align-items: center;
          gap: 8px;
          background-color: #FFF;
          border-radius: 8px;
          height: 50px;
          padding-left: 16px;
          @include rtl-styles {
            margin-right: rem(0);
          }
          &__feild {
            display: flex;
            align-items: center;
          }
        }
      }
    }
    &__message {
      font-size: rem(10);
      color: $grey-text;
      font-weight: 400;
      line-height: 16px; 
      letter-spacing: -0.1px;
    }
  }

  .receiver-myself {
    padding: rem(8);
    margin-bottom: rem(16);
    display: flex;
    flex-direction: column;
    height: 90px;
    border-radius: 12px;
    background-color: $medium-light-grey;

    &__details {
      margin-left: 8px;
      font-size: rem(18);
      color: $title-color;
      span {
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
        color: $dark-charcoal;
      }
    }
    &__container{
       height: 50px;
       background-color: $white;
       width: 100%;
       border-radius: 8px;
       display: flex;
       align-items: center;
       padding-left: 16px;
       margin-bottom: 8px;
       &--icon{
        height: 24px;
        width: 24px;
       }
    }
  }
  p{
    font-size: 10px;
    font-weight: 400;
    line-height: 16px;
    letter-spacing: -0.1px;
    color: $grey-text;
  }

  .delivery-section {
    &__container {
      border-radius: 12px;
      background-color: $medium-light-grey;
      margin-bottom: rem(24);
      display: flex;
      flex-direction: column;
      .delivery-header {
        padding: rem(13) rem(16);
        height: 58px;
        background-color: $white;
        border-radius: 8px;
        display: flex;
        align-items: center;
        margin: 8px;
        &--expand {
          justify-content: flex-start;
          position: relative;
          height: 48px;
        }
        &__clear {
          position: absolute;
          right: 16px;
          @include rtl-styles {
            left: 16px;
            right: unset;
          }
          a {
            font-size: rem(14);
            font-weight: 600;
            line-height: 18px;
            letter-spacing: -0.14px;
            color: $semi-dark-purple;
            text-decoration: underline;
          }
        }
        &__left {
          display: flex;
          align-items: center;
          margin-right: rem(8);
          @include rtl-styles {
            margin-right: 0;
            margin-left: rem(10);
          }
          p {
            color: $dark-charcoal;
            font-size: rem(16);
            margin-left: rem(8);
            font-weight: 600;
            line-height: 24px;
            @include rtl-styles {
              margin-right: rem(20);
            }
          }
          span {
            font-weight: normal;
          }
        }
        &__right {
          width: 105px;
          height: 32px;
          border-radius: 8px;
          background-color: $medium-light-grey;
          cursor: pointer;
          display: flex;
          align-items: center;
          padding-left: 8px;
          cursor: pointer;
          @include rtl-styles {
            padding-right: rem(8);
          }
          &--expand {
            .selector {
              span {
                color: $semi-dark-purple;
              }
            }
          }

          span {
            font-size: rem(16);
            font-weight: 600;
            line-height: 24px;
            color: $dark-charcoal;
          }
        }
      }
      .delivery-scheduler {
        margin: 0px 8px 8px 8px;
        &__date-picker {
          height: 340px;
          border-radius: 16px;
          padding: rem(16);
          background-color: $white;
          overflow: hidden;
        }
        .separator {
          margin: rem(13) 0 rem(20) 0;
          height: 1px;
          width: 100%;
          background-color: $silver;
        }
        &__selected-date {
          display: flex;
          justify-content: center;
          gap: 10px;
          font-size: rem(14);
          font-weight: 500;
          color: $semi-dark-purple;
          padding-bottom: 16px;
          border-bottom: 1px solid $grey-stroke;
          line-height: 18px; 
          letter-spacing: -0.14px;
          .delivery-info {
            color: $title-color;
          }
          .date-info-separator {
            width: 1px;
            font-weight: 100;
            display: inline-block;
            height: 14px;
            margin-top: rem(3);
            background-color: $semi-dark-purple;
            opacity: 0.3;
          }
        }
        &__time-selector {
          display: flex;
          gap: 16px;
          margin-top: rem(8);
        }
      }
    }
    &__message {
      font-size: rem(14);
      color: $text-grey;
      ul {
        list-style: disc;
        margin-left: rem(15);
        @include rtl-styles {
          margin-right: rem(15);
        }
      }
      &-info {
        display: flex;
        align-items: center;
        gap: 8px;
        background-color: #ffe4fb;
        padding: 20px 16px;
        font-size: 12px;
        border-radius: 6px;
        height: 48px;
      }
      &-info-gift {
        font-size: rem(12);
        font-weight: 500;
        line-height: 16px;
        letter-spacing: -0.1px;
        color: $text-grey;
        margin-bottom: rem(16);
        background: rgba(210, 201, 255, 0.25);
        padding: rem(16);
        border-radius: rem(12);

        li {
          width: 98%;
          line-height: 18px;
          display: flex;
          align-items: center;
          border-radius: 50%;
          gap: 7px;

          &:nth-child(n+2){
            margin-top: 4px;
          }

          span{
            display: inline-block;
            height: 3px;
            width: 3px;
            background: $text-grey;
            border-radius: 12px;
            
          }

          &::marker {
            color: #545454;
            font-size: 9px;
          }
        }
      }
    }
  }
  .create__button {
    width: 100%;
    height: 50px;
    font-size: rem(16);
    font-weight: 700;
    letter-spacing: -0.16px;
  }
}
