@import '@/styles/abstracts/abstracts';

.download-app {
  border-radius: 12px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-bottom: rem(80);
  margin-top: rem(80);

  &__content {
    padding: rem(75) rem(57) rem(70) 0;
    margin: 0 0 0 90px;

    @include rtl-styles {
      margin: 0 90px 0 0;
      padding: rem(75) 0 rem(70) rem(57);
    }

    &-header {
      margin-bottom: rem(35);

      h2 {
        width: 439px;
        line-height: rem(40);
        font-size: rem(32);
        font-weight: 800;
        color: $dark-charcoal;
        letter-spacing: -0.16px;
        font-family: var(--font-bricolage); font-optical-sizing: none;
      }
    }

    &-links {
      display: flex;
      align-items: center;
      gap: 24px;

      &-qr-code {
        width: 200px;
        height: 200px;
        background-color: $white;
        border-radius: 20px;

        img{
          border-radius: 20px;
          width: 100%;
          height: 100%;
          object-fit: fill;
        }
      }

      &-stores {
        div {
          width: 160px;
          height: 51.5px;
          background-color: $white;
          margin-bottom: rem(20);

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }
  }

  &__img-block {
    position: absolute;
    height: 100%;
    width: 49.5%;
    left: 0;

    @include rtl-styles {
      left: unset;
      right: 0;
      height: 100%;
    }
    > img {
      height: 100%;
      width: 100%;
      object-fit: cover;
      aspect-ratio: 359/276;
    }
  }
}
