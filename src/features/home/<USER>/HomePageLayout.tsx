'use client';
import { useEffect, useState } from 'react';
import getRegionAndLocale from '@/utils/getRegionAndLocale';
import useHomeAPI from '@/features/home/<USER>';
import GGStepSection from '@/features/home/<USER>/GGStepSection';
import CardsSlider from '@/features/home/<USER>/CardsSlider';
import HappyYouCard from '@/features/home/<USER>/HappyYouCard';
import OccasionSection from '../occasionSection/OccasionSection';
import HomeBanner from '../homeBanner/HomeBanner';
import GiftsBanner from '../giftsBanner/GiftsBanner';
import { useSearchParams } from 'next/navigation';
import useCommonSlice from '@/features/common/commonSlice';
import { useAppSelector, useAppDispatch } from '@/redux/hooks';
import RewardsHomeComponents from '../homeRewardsLayout/RewardsHomeComponents';
import Divider from '@/features/common/divider/divider';

/**
 * @method HomePageLayout
 * @description HappyYouCard component
 * @returns {JSX.Element}
 */
const HomePageLayout = ({userInfo, landingConfig}: any): JSX.Element => {
  //#.get locale region
  const { locale } = getRegionAndLocale();
  const searchParams = useSearchParams();
  const isRewards = searchParams.get('embedded');
  const dispatch = useAppDispatch();
  // #.Get the isUserSignedIn from userInfo
  const { isUserSignedIn, refreshToken }: any = userInfo;
  const { getTokenInfo, setIsDownloadAppVisible } = useCommonSlice();
  const { AccessToken }: any = useAppSelector(getTokenInfo);
  const [giftItems, setGiftItems] = useState<Array<any>>([]);
  const [invitedItems, setInvitedItems] = useState<Array<any>>([]);
  const [loading, setLoading] = useState<boolean>(true);

  // #. Decalre variables
  const DATA_LIMIT: number = 10;

  // #. getting my gifts api data
  const { FetchMyGroupGifts, FetchInvitedGifts } = useHomeAPI();
  const { groupGiftData, groupGiftLoading, fetchMoreGiftsItems } =
    FetchMyGroupGifts(
      giftItems.length < DATA_LIMIT ? DATA_LIMIT : giftItems.length,
      '',
      locale,
      isRewards ? AccessToken : userInfo?.accessToken
    );

  // #. getting invited api data
  const { invitedGiftData, invitedGiftLoading, fetchMoreInvitedItems } =
    FetchInvitedGifts(
      invitedItems.length < DATA_LIMIT ? DATA_LIMIT : invitedItems.length,
      '',
      locale,
      userInfo?.accessToken
    );

  useEffect(() => {
    if (!groupGiftLoading && groupGiftData?.createdGiftsList) {
      setGiftItems(groupGiftData?.createdGiftsList?.edges);
      setLoading(false);
    }
  }, [groupGiftLoading]);

  useEffect(() => {
    if (!invitedGiftLoading && invitedGiftData?.invitedGiftsList) {
      setInvitedItems(invitedGiftData?.invitedGiftsList?.edges);
    }
  }, [invitedGiftLoading]);

  // #. Getting API data
  // const { FetchLandingPageData } = useHomeAPI();
  // const { landingPageData } = FetchLandingPageData(locale);

  const hasInvitedGifts = (invitedGiftData?.invitedGiftsList?.edges?.length as any) > 0;
  const hasCreatedGifts = (groupGiftData?.createdGiftsList?.edges?.length as any) > 0;
  const hasGifts = hasInvitedGifts || hasCreatedGifts;
  const isLoading = invitedGiftLoading || groupGiftLoading
  
  const showDownloadApp = (!refreshToken || (refreshToken && !hasGifts)) && !isLoading;
  const showHappyYouCard = (!refreshToken || (refreshToken && hasGifts)) && !isLoading;

  useEffect(()=>{
    dispatch(setIsDownloadAppVisible(showDownloadApp));
  },[showDownloadApp])
  
  return !isRewards ? (
    <>
      {!refreshToken && (
        <HomeBanner
          isUserSignedIn={refreshToken}
          landingPageData={landingConfig}
        />
      )}
      {!refreshToken && <OccasionSection />}
      {!refreshToken && <GGStepSection landingPageData={landingConfig} />}
      {refreshToken && <GiftsBanner />}
      {refreshToken && (
        <CardsSlider
          invitedData={invitedGiftData}
          invitedLoading={invitedGiftLoading}
          invitedFetchMore={fetchMoreInvitedItems}
          giftsData={groupGiftData}
          giftsLoading={groupGiftLoading}
          giftsFetchMore={fetchMoreGiftsItems}
        />
      )}
      {showHappyYouCard && <HappyYouCard landingPageData={landingConfig} />}
      <Divider/>
    </>
  ) : (
    <RewardsHomeComponents
      giftLength={groupGiftData?.createdGiftsList?.edges?.length}
      loading={loading}
      invitedData={invitedGiftData}
      invitedLoading={invitedGiftLoading}
      invitedFetchMore={fetchMoreInvitedItems}
      giftsData={groupGiftData}
      giftsLoading={groupGiftLoading}
      giftsFetchMore={fetchMoreGiftsItems}
    />
  );
};

export default HomePageLayout;
