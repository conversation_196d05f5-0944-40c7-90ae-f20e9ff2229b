import React, { useEffect, useState } from 'react';
import styles from './HomeBanner.module.scss';
import Button from '@/features/common/button/Button';
import { useTranslation } from 'react-i18next';
import Link from 'next/link';
import {
  CLEVERTAP_EVENTS,
  GTM_EVENTS,
  PAGEURLS,
  PLATFORM_TYPE,
} from '@/constants/common';
import useLoginRedirection from '@/utils/loginRedirection';
import getRegionAndLocale from '@/utils/getRegionAndLocale';
import GoogleTagManager from '@/utils/googleTagManager';
import { pushCleverTapEvent } from '@/features/common/clevertap/clevertap.services';
import Image from 'next/image';
import { Skeleton } from '@mui/material';
import { imageBaseUrl } from '@/constants/envVariables';
import CustomLink from '@/features/common/customLink/CustomLink';
/**
 * @method HomeBanner
 * @description Landing page Banner component (Non-Logged-in User)
 * @returns {JSX.Element}
 */
const HomeBanner = ({ isUserSignedIn, landingPageData }: any): JSX.Element => {
  const { t } = useTranslation();

  // #.Login redirection custom hook
  const { redirect } = useLoginRedirection();

  // #.Get locale and region
  const { locale, region }: any = getRegionAndLocale();
  const [isImageLoading, setIsImageLoading] = useState(true);

  const topBannerImage =
    landingPageData?.landingPageConfigurations?.landing_page_config
      ?.top_banner_config?.mock_up_img;
  /**
   * @method trackStartGroupGift
   */
  const trackStartGroupGift = () => {
    // #. Push GA4 event
    const { push: gtmPush } = GoogleTagManager();

    gtmPush(
      GTM_EVENTS.GROUP_GIFT_START,
      {
        store: region?.toUpperCase(),
      },
      false
    );

    // #. Push CleverTap data

    const clevertapData = {
      Platform: PLATFORM_TYPE.WEB,
      Store: region?.toUpperCase(),
    };
    pushCleverTapEvent(CLEVERTAP_EVENTS.GROUPGIFT_START, clevertapData);
  };

  /**
   * @method onClickStartGroupGift
   * @description handle start group gift redirection
   */
  const handleStartGroupGift = () => {
    if (isUserSignedIn) {
      trackStartGroupGift();
    } else {
      redirect(locale, region);
    }
  };

  return (
    <div className={`container ${styles['home-banner']}`}>
      <div className={styles['home-banner__left']}>
        <div className={styles['home-banner__left-header']}>
          <h3>{t('groupGifting')}</h3>
        </div>
        <div className={styles['home-banner__left-description']}>
          <h3>{t('groupGiftingInfo')}</h3>
        </div>
        <div className={styles['home-banner__left-button']}>
          {isUserSignedIn ? (
            <CustomLink hrefLink={PAGEURLS.START_GROUP_GIFT} prefetch={false}>
              <Button
                theme="primary"
                className={styles['gg__button']}
                arrow="arrow-forward"
                action={() => trackStartGroupGift()}
              >
                {t('startAGroupGift')}
              </Button>
            </CustomLink>
          ) : (
            <Button
              theme="primary"
              className={styles['gg__button']}
              arrow="arrow-forward"
              action={() => handleStartGroupGift()}
            >
              {t('startAGroupGift')}
            </Button>
          )}
        </div>
      </div>
      <div className={styles['home-banner__right']}>
        {!topBannerImage ? (
          <Skeleton
            variant="rectangular"
            width={608}
            height={608}
            animation="wave"
            style={{ borderRadius: 60 }}
          />
        ) : (
          <>
            <Image
              src={`${imageBaseUrl}/images/banner-bg-new.png`}
              width={608}
              height={608}
              alt="banner-bg"
              unoptimized={true}
            />
            <div className={styles['mobile-layout']}>
              <Image
                src={topBannerImage}
                width={366}
                height={522}
                alt="mobile-layout"
                blurDataURL={`${imageBaseUrl}/images/preload-image.jpeg`}
                placeholder="blur"
                priority
                onLoad={() => setIsImageLoading(false)}
                className={`${
                  isImageLoading ? styles['blur'] : styles['remove-blur']
                }`}
              />
            </div>
            <div className={styles['happy-you-card']}>
              <Image
                src={`${imageBaseUrl}/images/banner-happy-card.webp`}
                width={250}
                height={389}
                alt="happy-you-card"
                unoptimized={true}
              />
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default HomeBanner;
