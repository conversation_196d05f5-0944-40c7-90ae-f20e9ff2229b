'use client';
import React from 'react';
import styles from './HomePageSkeleton.module.scss';
import {
  SwiperSkeleton,
  HappyYouCardSkelton,
  BannerSkelton,
  GGStepSectionSkelton,
  DownloadAppSkeleton,
  OccasionSectionSkeleton,
} from '../';
import TabSelectionHeaderSkeleton from '@/features/common/header/tabSelectionHeader/TabSelectionHeaderSkeleton';

interface HomePageSkeletonProps {
  isLoggedIn?: boolean;
}

/**
 * @method HomePageSkeleton
 * @description Skeleton loader for the homepage that shows content placeholders instead of a blank screen with loader
 * @returns {JSX.Element}
 */
const HomePageSkeleton = ({
  isLoggedIn = false,
}: HomePageSkeletonProps): JSX.Element => {
  return (
    <>
      <div className={styles['home-page-skeleton']}>
        {!isLoggedIn ? (
          // Non-logged in user view
          <>
            <TabSelectionHeaderSkeleton />
            <BannerSkelton />
            <OccasionSectionSkeleton />
            <GGStepSectionSkelton />
            <HappyYouCardSkelton />
            <DownloadAppSkeleton />
          </>
        ) : (
          // Logged in user view
          <>
            <TabSelectionHeaderSkeleton />
            <SwiperSkeleton />
            <SwiperSkeleton />
            <HappyYouCardSkelton />
          </>
        )}
      </div>
    </>
  );
};

export default HomePageSkeleton;
