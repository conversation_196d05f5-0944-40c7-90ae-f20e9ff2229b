'use client';

import React from 'react';
import styles from './HomeDownloadApp.module.scss';
import { useTranslation } from 'react-i18next';
import Link from 'next/link';
import useHomeAPI from '../homeAPI';
import CustomLink from '@/features/common/customLink/CustomLink';
import useCommonSlice from '@/features/common/commonSlice';
import { useAppSelector } from '@/redux/hooks';

/**
 * @method HomeDownloadApp
 * @description Download wrapper in homepage component
 * @returns {JSX.Element}
 */

const HomeDownloadApp = ({ locale }: { locale: any }):  JSX.Element | null => {
  const { t } = useTranslation();
  const { useDownloadApp } = useHomeAPI();
  const { data } = useDownloadApp();
  const { getIsDownloadAppVisible } = useCommonSlice();
  const isDownloadAppVisible: any = useAppSelector(getIsDownloadAppVisible);
  
  if(!isDownloadAppVisible){
    return (null);
  }

  const currentLocale = locale;
  const appBanner = data?.downloadApp[0]?.appBanner;
  const qrCode = data?.downloadApp[0].appDownloadQrcode;
  const bannerImage =
    currentLocale === 'ar'
      ? data?.downloadApp[0].bannerImage
      : data?.downloadApp[0].bannerImage;

  return (
    <div className={`container `}>
      <div className={styles['download-app']}>
        <div className={styles['download-app__img-block']}>
          <img src={bannerImage} alt="" />
        </div>
        <div className={styles['download-app__content']}>
          <div className={styles['download-app__content-header']}>
            <h2>{t('downloaApp')}</h2>
            <h2>{t("enjoyExp")}</h2>
          </div>
          <div className={styles['download-app__content-links']}>
            <div className={styles['download-app__content-links-qr-code']}>
              <img src={qrCode} alt="" />
            </div>
            <div className={styles['download-app__content-links-stores']}>
              {appBanner?.map((item: any, index: number) => (
                <div key={item?.id} className={styles['app-banner-items']}>
                  <CustomLink hrefLink={item?.itemUrl} aTarget="_blank">
                    <img src={item?.itemImage} alt="" />
                  </CustomLink>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HomeDownloadApp;
