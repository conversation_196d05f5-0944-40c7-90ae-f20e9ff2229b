// -----------------------------------------------------------------------------
// This file contains vendor overrides.
// -----------------------------------------------------------------------------

// MUI theme override

/* stylelint-disable */

//brands included component
.brands-search {
  .MuiTextField-root {
    width: 100%;
    background-color: $medium-light-grey;
    border-radius: 12px;
  }
  .MuiInputBase-root.MuiOutlinedInput-root {
    height: 60px;
    padding: 18px 24px;
    border-radius: 12px;
    background-color: $medium-light-grey;
    .MuiInputBase-input {
      color: $dark-charcoal;
      font-size: rem(16);
      font-weight: 600;
      line-height: 24px;
      padding-left: 16px;
    }
  }
  .MuiOutlinedInput-notchedOutline.MuiOutlinedInput-notchedOutline {
    border: none;
  }
  .MuiOutlinedInput-root input::placeholder {
    font-size: rem(12);
    color: $text-grey;
  }
  .MuiInputAdornment-root {
    width: 26px;
    margin-right: 4px;
  }
}
//gift creation Text Feilds
.creation-form {
  .MuiInputAdornment-root {
    margin-right: rem(8);
    width: 27px;
    @include rtl-styles {
      margin-left: rem(8);
    }
  }
  .MuiFormControl-root {
    .MuiFormHelperText-root {
      color: $mild-red;
      font-size: rem(10);

      @include rtl-styles {
        font-family: var(--font-noto-kufi);
      }
    }
  }
  .MuiInputBase-root.MuiInput-root:before,
  .MuiInputBase-root.MuiInput-root:after {
    border-bottom: none;
  }
  .MuiInput-root:hover:not(.Mui-disabled, .Mui-error):before {
    border-bottom: none;
  }

  .MuiInputBase-input.MuiInput-input {
    font-size: rem(16);
    font-weight: 600;
    color: $dark-charcoal;
    line-height: 24px;
    &::placeholder {
      color: rgba(0, 0, 0, 0.3);
    }
  }
  .phone-number-feild {
    .MuiInputBase-input.MuiInput-input {
      width: 370px;
      padding-left: 4px;
      @include rtl-styles {
        width: 348px;
      }
    }
  }

  .email-feild .MuiFormHelperText-root,
  .name-feild .MuiFormHelperText-root,
  .phone-number-feild .MuiFormHelperText-root,
  .organiser-feild .MuiFormHelperText-root,
  .title-feild .MuiFormHelperText-root {
    // position: absolute;
    bottom: -50%;
    left: 0;
    color: $error-text !important;
    font-size: rem(12);
    font-weight: 400;
    margin-bottom: 4px;
    @include rtl-styles {
      left: 0;
      right: unset !important;
    }
  }
}

//gift creation form separator
.form-separator {
  display: none;
  height: 1px;
  background-color: $lighter-grey;
  width: 85%;
}

//gift settings component
.gift-settings {
  &__more-settings {
    .MuiSwitch-root {
      .MuiButtonBase-root.MuiSwitch-switchBase {
        .MuiSwitch-input {
          left: 0 !important;
        }
      }
    }
  }
  //checkbox
  .MuiButtonBase-root.MuiCheckbox-root {
    width: 20px;
    height: 20px;
    padding: 0;
    margin-right: rem(10);
    @include rtl-styles {
      margin-right: 0;
      margin-left: rem(10);
    }
  }

  //Accordion
  .MuiPaper-root.MuiAccordion-root {
    box-shadow: none;
  }
  .MuiButtonBase-root.MuiAccordionSummary-root {
    display: inline-flex;
    padding: 0;
    align-items: baseline;
  }
  .MuiAccordionSummary-content,
  .MuiAccordionSummary-content.Mui-expanded {
    margin-right: rem(8);
    margin-bottom: rem(30);
    margin-top: 0;
    @include rtl-styles {
      margin-left: rem(6);
    }
  }
  .MuiAccordionDetails-root {
    padding: 0;
    margin-bottom: rem(32);
  }
  .MuiButtonBase-root.MuiAccordionSummary-root.Mui-expanded {
    min-height: 0;
  }

  //more settings collection feild
  .collection-field {
    .MuiInputBase-root.MuiOutlinedInput-root {
      font-size: rem(18);
      font-weight: 700;
      color: $dark-charcoal;
      border-radius: 12px;
      padding-left: rem(0);
      height: 50px;
      margin-left: 28px;

      @include rtl-styles {
        margin-left: 0;
        margin-right: 28px;
      }
    }
    .MuiOutlinedInput-notchedOutline.MuiOutlinedInput-notchedOutline {
      border: 1px solid $grey-stroke;
    }
    .fixed-amount-feild .MuiFormHelperText-root,
    .target-amount-feild .MuiFormHelperText-root,
    .suggested-amount-feild .MuiFormHelperText-root {
      position: absolute;
      top: 100%;
      left: 0;
      color: $mild-red;
      font-size: rem(12);
      font-weight: 500;
      margin-top: rem(8);
      margin-left: 30px;
      margin-bottom: rem(24);
      @include rtl-styles {
        right: 0;
        left: unset !important;
        margin-right: 30px;
      }
    }
    &-select {
      .MuiInputBase-root.MuiOutlinedInput-root {
        margin-left: 50px;
        font-size: rem(18);
        font-weight: 700;
        color: $dark-charcoal;
        border-radius: 12px;
        padding-left: rem(0);
        height: 50px;
      }
      .MuiOutlinedInput-notchedOutline.MuiOutlinedInput-notchedOutline {
        border: 1px solid $grey-stroke;
      }
      .fixed-amount-feild .MuiFormHelperText-root,
      .target-amount-feild .MuiFormHelperText-root,
      .suggested-amount-feild .MuiFormHelperText-root {
        position: absolute;
        top: 100%;
        left: 0;
        color: $mild-red;
        font-size: rem(12);
        font-weight: 500;
        margin-top: rem(8);
        margin-left: 50px;
        margin-bottom: rem(24);
        @include rtl-styles {
          right: 0;
          left: unset !important;
        }
      }
    }
  }
}

//delivery scheduler
.delivery-scheduler {
  .MuiPickerStaticWrapper-content {
    div.mui-epd502 {
      margin: 0;
      width: calc(100% - 3px);
      justify-content: space-between;
    }
    .MuiCalendarPicker-root {
      width: 100%;
      margin-top: 16px;
      div.mui-1dozdou {
        margin-top: 0;
        padding: 0;
        position: relative;
        width: 100%;
        @include rtl-styles {
          width: 98%;
          margin-right: rem(5);
        }
        > div {
          position: absolute;
          left: 50%;
          transform: translateX(-50%);
          font-size: rem(16);
          font-weight: 600;
          line-height: 24px;
          padding: 0;
          color: $dark-charcoal;
          .MuiButtonBase-root.MuiIconButton-root {
            display: none;
          }
        }
        .MuiPickersArrowSwitcher-root {
          button {
            display: flex !important;
            width: 25.94px;
            height: 24.319px;

            @include rtl-styles {
              transform: rotate(180deg);
            }
          }
        }
      }
      .PrivatePickersFadeTransitionGroup-root {
        .MuiTypography-caption {
          color: $grey-stroke;
        }
        .PrivatePickersSlideTransition-root {
          .Mui-disabled {
            color: $grey-stroke !important;
          }
          div.mui-mvmu1r {
            .MuiPickersDay-root {
              color: $dark-charcoal;
              font-weight: 500;
              font-size: 12px;
            }
            .Mui-selected {
              color: $white;
            }
          }
        }
      }
    }
  }

  .MuiPickersArrowSwitcher-root {
    width: 100%;
    position: absolute;
    justify-content: space-between;
    z-index: 1;
  }

  .MuiCalendarPicker-viewTransitionContainer {
    div.mui-1n2mv2k {
      justify-content: space-between;
    }
  }

  .PrivatePickersSlideTransition-root {
    div.mui-i6bazn {
      > div {
        height: 28px;
      }
      div.mui-mvmu1r {
        justify-content: space-between;
        width: 497px;

        .MuiPickersDay-root {
          width: 29px;
          border-radius: 8px;
        }
      }
    }
  }

  .MuiButtonBase-root.MuiPickersDay-root.Mui-selected {
    background-color: $semi-dark-purple;
    border-radius: 8px;
  }

  .MuiButtonBase-root.MuiPickersDay-root {
    width: 28px;
    height: 29px;
  }

  .MuiButtonBase-root.MuiIconButton-root.MuiPickersArrowSwitcher-button {
    border-radius: 6px;
    border: 1px solid $silver;
    width: 30px;
    height: 30px;
    &:hover {
      background-color: transparent;
    }
  }
}

//timezone selector
.selector-timezone {
  .MuiInputBase-root {
    border-radius: 12px;
    width: 176px;
    height: 44px;
    background-color: $white;
    font-size: rem(14);
    font-weight: 500;
    color: $semi-dark-purple;
    padding-right: rem(16);
    padding-left: rem(16);
    @include rtl-styles {
      padding-left: rem(10);
    }
  }
  .MuiOutlinedInput-notchedOutline {
    border: none;
  }
  .MuiSelect-select.MuiInputBase-input.MuiOutlinedInput-input.MuiSelect-select.MuiInputBase-input.MuiOutlinedInput-input.MuiSelect-select.MuiInputBase-input.MuiOutlinedInput-input {
    @include rtl-styles {
      padding: 0 0 0 rem(32);
    }
  }
}
.MuiButtonBase-root.MuiIconButton-root.MuiPickersToolbar-penIconButton {
  display: none !important;
}

.MuiTimePickerToolbar-hourMinuteLabel {
  margin-bottom: 17px !important;
}
//time selector
.selector-time {
  .MuiInputBase-root input {
    color: $semi-dark-purple;
    text-transform: uppercase;
    height: 44px;
    padding: 0 0 0 16px;
    background-color: $white;
    border-radius: 12px;
    font-weight: 500;
    @include rtl-styles {
      padding: 0 rem(15) 0 0;
    }
  }
  .MuiInputBase-root {
    width: 176px;
  }
  fieldset {
    border: 1px solid $white;
    border-color: transparent !important;
  }
  .MuiPickersLayout-root {
    display: none;
  }
}

.MuiTimePickerToolbar-ampmSelection {
  @include rtl-styles {
    margin-left: auto;
    margin-right: 12px;
  }
}

.select-menu-item {
  border-radius: 12px !important;
  .MuiButtonBase-root.MuiMenuItem-root {
    color: $dark-charcoal;
    font-weight: 500;
  }
}

.selector-timezone-dialog {
  //hide arrow mark
  .MuiPickersArrowSwitcher-root.MuiTimeClock-arrowSwitcher {
    display: none;
  }
  .MuiDialog-paper {
    width: 320px;
  }
  .MuiTypography-root {
    font-size: rem(14);
    font-weight: 500;

    @include rtl-styles {
      font-family: var(--font-noto-kufi);
    }
  }
  .MuiDialogActions-root {
    button {
      font-size: rem(12);
      color: $black;
      &:hover {
        color: $semi-dark-purple;
      }
    }
  }
}

//view example
.message-dialog {
  .MuiPaper-root.MuiDialog-paper {
    padding: rem(16) rem(24) rem(24) rem(24);
    border-radius: 12px;
    min-width: 660px;
    background-color: $medium-light-grey;
    position: absolute;
    top: 12%;
  }

  .MuiTypography-root.MuiDialogTitle-root {
    font-size: rem(16);
    font-weight: 600;
    padding: 0;
  }
  .MuiDialogTitle-root + .MuiDialogContent-root {
    padding-top: rem(20);
  }
  .MuiDialogContent-root {
    padding: 0;
    overflow-y: visible;
  }
  .MuiDialogActions-root {
    padding: 0;
    margin: 0;
    justify-content: center;
  }
}
.gift-selection {
  &__occasion {
    .MuiInputBase-root {
      background-color: $white;
      border-radius: 12px;
      border: solid 1px $grey-stroke;
      font-size: rem(18);
      font-weight: 600;
      color: $dark-charcoal;
      padding-right: rem(16);
      height: 50px;
      @include rtl-styles {
        padding-left: rem(20);
      }
    }

    .MuiSelect-select {
      padding-right: 0 !important;
    }
    .MuiOutlinedInput-notchedOutline {
      border: none;
    }
  }
}

.groupgift-right-header {
  .MuiInputBase-root {
    width: 170px;
    height: 50px;
    background-color: $white;
    border-radius: 100px;
    border: none;
    background-color: #f2f5f8;
    font-size: rem(18);
    font-weight: 600;
    color: $title-color;
    padding-right: 16px;
    padding-left: 16px;
    @include rtl-styles {
      padding-left: rem(20);
      padding-right: 0;
    }
  }
  .MuiOutlinedInput-notchedOutline {
    border: none;
  }
  .MuiSelect-select {
    div {
      display: flex;
      align-items: center;
      gap: 10px;
      span {
        font-size: 14px;
        font-weight: 500;
      }
    }
  }
}

.store-selector.store-selector {
  .rfm-marquee-container {
    width: 160px;
  }
  border-radius: 12px;
  .MuiButtonBase-root.MuiMenuItem-root {
    &:hover,
    &:active,
    &.Mui-selected {
      background-color: #e6f7ff;
      font-weight: 500;
    }
    div {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
}

// Contribution Progress bar
.amount-selection {
  .MuiLinearProgress-root {
    background-color: $pale-grey;
    .MuiLinearProgress-bar1Determinate {
      background-color: $barney-purple;
    }
  }
}

.brand-greetings {
  .swiper {
    padding-bottom: 20px;
    min-height: 497px;
    min-width: 100%;
  }
  .next-greeting {
    height: 70px;
    width: 70px;
    border-radius: 50%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    color: $barney-purple;
    position: absolute;
    right: 0;
    top: 40%;
    z-index: 1;

    @include rtl-styles {
      right: -20px;
      // transform: rotate(180deg);
      // margin-right: 7px !important;
      margin-left: 0;
    }

    &::after {
      display: none;
    }

    img {
      display: block;
    }
  }
  .prev-greeting {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    color: $barney-purple;
    position: absolute;
    transform: rotate(180deg);
    left: 0;
    top: 40%;
    z-index: 1;
    @include rtl-styles {
      left: -20px;
      // transform: rotate(360deg);
      margin-left: 0;
    }

    &::after {
      display: none;
    }

    img {
      display: block;
    }
  }

  .swiper-button-disabled {
    opacity: 0.5;
  }
}

.brand-video-editor {
  .MuiDialogContent-root {
    padding: 0 !important;
  }

  .MuiDialog-paper {
    margin: 0 16px;
    padding: 0 !important;
  }
}

// .MuiDialogContent-root {
//   padding: 0 !important;
// }

.brand-video-editor-tool {
  width: 784px;
  height: 525px;
  overflow: hidden;

  .camera_tag {
    width: 100% !important;
    height: 100% !important;
  }
}

.brand-video-editor-cancel {
  display: block;
  text-align: center;
  background: $pale-grey;
  color: $barney-purple;
  font-size: 16px;
  margin-top: 18px;
  font-weight: 500;

  &:hover {
    color: $barney-purple;
  }
}

.MuiDialog-paper {
  margin: 0 16px !important;
  border-radius: 12px !important;
}

// Camera tag custom changes for personalisation section

#ygag-camera {
  font-family: var(--font-mona-sans);
  @include rtl-styles {
    font-family: var(--font-noto-kufi);
  }

  .cameratag_screen.cameratag_start {
    background: $medium-light-grey;
  }

  .cameratag_select_prompt {
    visibility: hidden;
  }

  .cameratag_settings_btn {
    display: none !important;
  }

  #ygag-camera__start-el-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 12px;
    background-color: $pale-grey;
    padding: 12px;
    border-radius: 12px;
    min-height: 164px;
    position: relative;
    z-index: 99;

    a.cameratag_primary_link {
      margin-top: 0;
      background-color: $white;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 152px;
      width: 50%;
      color: $barney-purple;

      .cameratag_prompt_label {
        font-family: var(--font-mona-sans);
        @include rtl-styles {
          font-family: var(--font-noto-kufi);
        }
        font-weight: 700;
        bottom: 25px;
        font-size: 16px !important;
        line-height: 24px;
        letter-spacing: -0.16px;
      }

      .cameratag_action_icon {
        @include font-size-important(32);

        margin-top: -40px;
      }

      br {
        display: none;
      }

      i.icon-video-camera {
        @include font-size-important(22);
      }
    }

    a.cameratag_record {
      img {
        margin-bottom: 30px;
      }
    }

    .cameratag_primary_link.cameratag_upload {
      img {
        width: 32px;
        margin-top: -35px;
      }
    }

    .ygag-camera__or-placeholder {
      font-size: 16px !important;
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
      z-index: 9;
      border-radius: 50%;
      height: 52px;
      width: 52px;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: $medium-light-grey;
      color: $dark-charcoal;
      text-transform: lowercase;
      font-weight: 700;
      line-height: 24px;
      letter-spacing: -0.16px;
    }
  }

  .cameratag_error {
    background: $medium-light-grey;
    .cameratag_error__wrapper {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 100%;

      h3 {
        font-size: 24px !important;
        font-family: var(--font-mona-sans);
        @include rtl-styles {
          font-family: var(--font-noto-kufi);
        }

        color: $dark-purple;
        margin: -100px 0 12px !important;
        font-weight: 600;
      }

      span.error-message {
        font-size: 16px !important;
        font-family: var(--font-mona-sans);
        @include rtl-styles {
          font-family: var(--font-noto-kufi);
        }

        color: $warm-grey;
        font-weight: 200;
        width: 570px;
        line-height: 1.5;
      }

      button.error-close-button {
        font-size: 16px !important;
        font-family: var(--font-mona-sans);
        @include rtl-styles {
          font-family: var(--font-noto-kufi);
        }

        color: $barney-purple;
        padding: 10px 32px;
        border: 1px solid $barney-purple;
        border-radius: 12px;
        background-color: $white;
        margin-top: 30px;
        cursor: pointer;
        font-weight: 500;
      }
    }
  }
}
.camera_tag {
  .cameratag_screen.cameratag_completed .cameratag_thumb_bg {
    background-repeat: no-repeat;
    background-size: cover !important;
    background-position-y: center;
    display: flex;
    justify-content: center;
    &::after {
      content: '';
      background: black;
      position: absolute;
      height: 100%;
      width: 100%;
      opacity: 0.4;
    }
    img {
      position: absolute;
      top: 40%;
      z-index: 2;
    }
  }
  .cameratag_screen.cameratag_completed .cameratag_checkmark {
    text-transform: capitalize;
    font-size: 16px !important;
    font-family: 'Mona Sans';
    top: 58% !important;
    z-index: 9;
  }

  // CameraTag Accept screen

  .cameratag_screen.cameratag_accept {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
    flex-wrap: wrap;
    height: 45%;
    width: 500px;
    margin: auto;
  }
  .cameratag_screen.cameratag_accept.accept_border {
    border: 10px $white solid;
  }

  .cameratag_screen.cameratag_accept.dark_overlay::before {
    content: '';
    // background: #000000;
    opacity: 0.5;
    height: 100%;
    width: 100%;
    position: absolute;
    z-index: 1;
  }

  .cameratag_screen.cameratag_wait {
    z-index: 10;
  }
  .cameratag_screen.cameratag_accept.hide_element {
    display: none !important;
  }

  .cameratag_screen.cameratag_accept .cameratag_accept_btn,
  .cameratag_screen.cameratag_accept .cameratag_accept_btn,
  .cameratag_screen.cameratag_accept .cameratag_rerecord_btn,
  .cameratag_screen.cameratag_accept .cameratag_play_btn {
    border: none !important;
    background: none !important;
    height: 15% !important;
    font-size: 16px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 20px !important;
    color: $white !important;
    z-index: 9;
  }
  .cameratag_play_btn.cameratag_play {
    order: 1;
    -webkit-order: 1;
    width: 100%;
  }
  .cameratag_rerecord_btn.cameratag_record {
    order: 2;
    -webkit-order: 2;
    width: 45%;
  }
  .cameratag_accept_btn.cameratag_publish {
    order: 3;
    -webkit-order: 3;
    width: 45%;
  }

  .cameratag_prompt,
  .cameratag_upload_status {
    display: none !important;
  }
  .radial-progress {
    background-color: transparent !important;
    width: 384px !important;
    top: 50% !important;
    transform: translateY(-97px) !important;
  }
  .radial-progress .circle .shadow {
    box-shadow: none !important;
  }
  .radial-progress .inset {
    display: none !important;
  }
  .uploading_progress {
    align-self: center;
    progress {
      width: 95%;
      height: 5px;
      border: 0px;
      border-radius: 20px;
    }
    progress::-webkit-progress-value {
      background: $barney-purple !important;
      border-radius: 20px;
    }

    progress::-moz-progress-bar {
      background: $barney-purple !important;
      border-radius: 20px;
    }

    progress::-ms-fill {
      background: $barney-purple !important;
      border-radius: 20px;
    }
    .bottomCont {
      display: flex;
      justify-content: space-between;
      margin: 13.5px 10px 0;

      p {
        font-size: 16px;
        margin: 0;
      }
    }
  }
  &.rtl {
    direction: rtl;
  }
}
.brand-video-editor-close {
  position: absolute;
  z-index: 999;
  cursor: pointer;
  height: 32px;
  width: 32px;
  right: 16px;
  top: 16px;

  @include rtl-styles {
    left: 16px;
    right: auto;
  }
}

.brand-photo-editor-tool {
  width: 769px;
  height: 450px;

  /* stylelint-disable */
  [data-test='MainBarButtonClose'] {
    display: none;
  }

  [data-test='NummericInput'] {
    padding: 0 !important;
    width: 30px !important;
  }

  [data-test='BrushSize'] > div {
    margin-right: -15px !important;
  }
  [data-test='BrushHardness'] > div {
    margin-right: -15px !important;
  }

  [data-test='BrushSize'] {
    width: 190px !important;
    max-width: 190px !important;
    margin-right: 40px;
  }

  [data-test='ToolControlBarExpandableControls'] {
    width: 100% !important;
    padding: 0 !important;
  }

  [data-test='Opacity'] + div > div > div {
    margin-right: -10px !important;
  }

  [data-test='Size'] + div > div > div {
    margin-right: -10px !important;
  }

  [data-test='ToolControlBarExpandableControls']
    > div
    > div:nth-child(2)
    > div {
    margin-right: -10px !important;
  }

  /* stylelint-enable */
}

.brand-gif-editor-tool {
  height: 450px;

  .giphy-search-bar {
    margin-bottom: 24px;
    position: relative;
    > svg {
    }
    input {
      background: $pale-grey;
      padding: 20px 20px 22px 32px;

      @include rtl-styles {
        padding: 20px 36px 22px 32px;
      }
      border-radius: 12px;
      font-size: 14px;

      &::placeholder {
        color: #545454;
        font-size: 14px;
      }
    }

    input + div {
      position: absolute;
      width: 34px;
      height: 34px;
    }
    input + div {
      > div {
        background: transparent;

        &:before {
          content: none;
        }
      }
      svg path {
        fill: black;
      }
    }

    input + svg + div {
      position: absolute;
      width: 34px;
      height: 34px;
      > div {
        background: transparent;

        &:before {
          content: none;
        }
      }
      svg path {
        fill: black;
      }
    }
  }
  .giphy-grid {
    @include rtl-styles {
      direction: ltr !important;
    }
  }
  .giphy-search-bar-cancel {
    margin-left: 20px;
    right: 0;

    @include rtl-styles {
      margin-right: 20px;
    }
  }

  .giphy-empty-message {
    color: #a6a6a6;
    display: flex;
    justify-content: center;
    margin: 30px 0;
    font-size: 16px;
    font-weight: 600;
  }
}

.brand-gif-editor {
  .MuiDialog-paper {
    padding: 17px;
    max-width: 832px;
  }

  .MuiDialogContent-root {
    padding: 0 !important;
  }
}
// Guest Login Textfields

.guest {
  .MuiOutlinedInput-root {
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
    border: 1px solid $dark-charcoal;

    &.Mui-focused {
      outline: 1px solid $dark-charcoal;
      fieldset {
        border: 0;
      }
    }
  }
  .MuiTextField-root {
    width: 100%;
    margin-bottom: rem(16);
  }
  .MuiInput-root:before,
  .MuiInput-root:after,
  .MuiInput-root:hover:not(.Mui-disabled, .Mui-error):before {
    border-bottom: 1px solid $narrow-grey;
  }

  .MuiInputLabel-root.Mui-focused {
    color: $text-grey;
  }

  .MuiInputLabel-root {
    @include rtl-styles {
      transform-origin: top right !important;
    }
  }
  .email-feild .MuiFormHelperText-root,
  .name-feild .MuiFormHelperText-root {
    color: $mild-red;
    font-size: rem(12);
    font-weight: 500;
    margin-left: 0;
    margin-top: 0;
  }
}

.gift-stepper {
  .MuiStepLabel-root {
    padding: rem(0) 0;
  }
  .MuiStepLabel-iconContainer {
    margin-right: rem(21);
    padding-right: 0;
    @include rtl-styles {
      margin-right: 0;
      margin-left: rem(21);
    }
  }

  .MuiStepConnector-root {
    @include rtl-styles {
      margin-right: 14px;
    }
    .MuiStepConnector-line {
      border-color: $grey-stroke !important;
      border-left-style: dotted;
      border-left-width: 2px;
      min-height: 47px;
      margin-left: 2px;
      @include rtl-styles {
        border-left: 0;
        border-right-style: dotted;
      }
    }
  }
  .MuiStepContent-root {
    border-color: $grey-stroke !important;
    border-left-style: dotted;
    border-left-width: 2px;
    margin-left: 14px;
    @include rtl-styles {
      border-left: 0;
      border-right-style: dotted;
      margin-right: 14px;
    }
  }
  .MuiStepContent-root.MuiStepContent-last {
    border-left: none;
    @include rtl-styles {
      border-right: none;
    }
  }
}

.hide-step {
  .MuiStepConnector-root {
    .MuiStepConnector-line {
      border-color: transparent !important;
      border-left-width: 0;
    }
  }

  .MuiStepContent-root {
    border-color: transparent !important;
    border-left-width: 0px;
    padding-right: 0;
    padding-left: 0;
    margin-left: 0;
  }

  .MuiStepLabel-root {
    visibility: hidden;
    display: none;
  }
}

//country phone code
.country-phone-code {
  ul {
    li.Mui-selected {
      background-color: $light-purple !important;
    }
  }
  .MuiSelect-select.MuiInputBase-input.MuiInput-input.MuiSelect-select.MuiInputBase-input.MuiInput-input.MuiSelect-select.MuiInputBase-input.MuiInput-input {
    padding-top: 8px;
    padding-right: 4px;
  }
  .MuiSelect-select.MuiInputBase-input.MuiInput-input:focus {
    background-color: transparent;
  }
}

.paper-signin-dropdowns {
  border-radius: 12px !important;
  border: 1px solid $grey-stroke;
  // box-shadow: none !important;
  margin-top: 6px;
  margin-left: -101px;

  ul {
    padding: 0;
    width: 100%;
  }

  // max-height: 300px !important;
  width: 360px;
  height: 400px !important;
}

//select gift occasion
.gift-occasion-select.gift-occasion-select {
  border-radius: 12px;
  max-height: 400px;
}

.gift-occasion-select {
  .MuiButtonBase-root {
    @include rtl-styles {
      font-family: var(--font-noto-kufi);
    }
  }
}

.account-details.account-details {
  position: relative;
  .DropDownMenu_drop-down-menu--active__ToMjZ.DropDownMenu_drop-down-menu--active__ToMjZ {
    border: solid 0.5px #d9dfe4;
  }
  .DropDownMenu_drop-down-menu__n6pc_.DropDownMenu_drop-down-menu__n6pc_ {
    border-radius: 0 0 4px 4px;
    position: absolute;
    left: unset !important;
    top: 47px;
    right: 13px !important;
    transform: none !important;

    @include rtl-styles {
      left: 145px;
      top: 47px;
    }
  }
}

//gift details modal
.gift-action-dialog {
  .-MuiDialog-container {
    height: auto;
  }
  .MuiPaper-root.MuiDialog-paper {
    padding: rem(24);
    width: 524px;
    bottom: 166px;
  }
  .MuiDialogContent-root {
    font-size: rem(14);
    color: $dark-charcoal;
    margin-bottom: rem(24);
    font-weight: 500;
    line-height: 18px;
    letter-spacing: -0.14px;
  }
  .MuiDialogActions-root {
    // justify-content: space-between;
    gap: rem(8);
    height: 50px;
    padding: 0;
  }
  .MuiDialogContent-root {
    padding: 0 0;
  }
}

.brands-included-dialog {
  .MuiDialogContent-root {
    padding: rem(24);
    width: 514px;
    height: 558px;
    background-color: $white;
    border-radius: 14px;
    overflow-y: hidden;
  }
}

.MuiAlert-message {
  font-size: rem(14);
  //snackbar
  .MuiSnackbar-root {
    .MuiAlert-message {
      font-size: rem(14);
      @include rtl-styles {
        font-family: var(--font-noto-kufi);
      }
    }
    .MuiAlert-icon {
      @include rtl-styles {
        margin-left: rem(12);
        margin-right: 0;
      }
    }
    .MuiAlert-action {
      @include rtl-styles {
        margin-right: rem(8);
        padding: 4px 0 0 0;
      }
    }
  }
}

.contribute-note {
  .MuiInputBase-root.MuiOutlinedInput-root {
    padding: rem(16);
    height: 180px;
    align-items: start;
    border-radius: 12px;
    font-size: rem(16);
  }
  .MuiInputBase-root.MuiOutlinedInput-root:hover
    .MuiOutlinedInput-notchedOutline {
    border: solid 1px rgba(144, 152, 161, 0.7);
  }
  .MuiInputBase-root.MuiOutlinedInput-root.Mui-focused
    .MuiOutlinedInput-notchedOutline {
    border: solid 1px rgba(144, 152, 161, 0.7);
  }

  .contribute-note-feild .MuiFormHelperText-root {
    position: absolute;
    top: 100%;
    left: 0;
    color: $error-text;
    font-size: rem(14);
    font-weight: 500;
    margin-top: rem(10);
    margin-left: 0;
    @include rtl-styles {
      right: 0;
      left: unset !important;
    }
  }
}

.circular-wrapper {
  .MuiCircularProgress-root {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 25px !important;
    height: 25px !important;
    svg {
      color: #fff;
    }
  }
}

.guest {
  .MuiFormHelperText-root {
    color: $error-text;
  }
}
// Gift open preview
.opening-introduction-group-gift .button-icon-wrapper {
  @include rtl-styles {
    rotate: -180deg;
  }
}
.button-spinner-wrapper {
  .MuiCircularProgress-root {
    color: $white;
  }
}

.contributer-edit {
  margin-top: 8px;
  .MuiMenu-list {
    padding-top: 0;
    padding-bottom: 0;
    border-radius: 12px !important;
  }
  .MuiMenuItem-root {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 200px;
    font-size: 16px;
    font-weight: 500;
    line-height: 16px;
    letter-spacing: -0.16px;
    color: $dark-charcoal;
    background-color: $white;
    padding: 12px 16px !important;
    height: 48px;
    border-bottom: 0.5px solid $grey-stroke;

    &:last-child {
      color: $mild-red;
      border-bottom: 0;
    }

    &:hover {
      background-color: #fff;
      color: $barney-purple;
    }
  }
  .Mui-selected {
    background-color: white;
  }
  .MuiMenu-paper {
    border-radius: 12px !important;
  }
}

.custom-snackbar {
  .MuiAlert-icon {
    @include rtl-styles {
      margin-left: 12px;
      margin-right: 0;
    }
  }
  .MuiAlert-action {
    @include rtl-styles {
      padding: 4px 16px 0 0 !important;
    }
  }
}

.contribute-drawer {
  .MuiFormHelperText-root {
    font-size: rem(12);
    font-weight: 500;
    color: $mild-red;
    margin-left: 0;
    margin-top: 0;
  }

  .MuiInput-root::before {
    border-bottom: 1px solid #e8ecf0;
  }

  .MuiInput-root::after {
    border-bottom: 1px solid #e8ecf0;
  }
  .MuiInput-root {
    border: none !important;
  }

  .MuiDrawer-paper {
    border-radius: 20px;
  }

  .MuiOutlinedInput-root {
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;

    &.Mui-focused {
      outline: 1px solid $dark-charcoal;
      fieldset {
        border: 0;
      }
    }
  }

  .MuiInput-input {
    font-size: rem(18);
    font-weight: 600;
    color: $dark-purple;
  }
}

.cart-badge {
  > span {
    @include font-size(10);
    
    background: $dark-charcoal;
    font-family: var(--font-mona-sans);
    z-index: auto;
    color: #fff;
    border-radius: 50%;
    font-weight: 700;
    white-space: nowrap;
    text-align: center;
    width: 18px ;
    height: 18px;
    top: -3px;
    right: -1px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 18px;
    display: flex;
    justify-content: center;
    align-items: center;
    letter-spacing: normal;

    @include rtl-styles {
      right: auto;
      left: -7px;
    }
  }
}

// #. Fix for marquee rtl
.rfm-marquee{
  @include rtl-styles{
    @keyframes arabic-scroll {
      0%{
        transform: translateX(100%);
      }
      100%{
        transform: translateX(0%);
      }
      
    }
    animation: arabic-scroll var(--duration) linear var(--delay) var(--iteration-count);
    animation-play-state: var(--play);
    animation-delay: var(--delay);
    animation-direction: var(--direction);
  }
}