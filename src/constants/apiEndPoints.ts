// #. Base API url for QA and other environments

const GROUPGIFT_GRAPHQL_BASE_URL =
  'https://groupgift-egg-ecomv2.sit.yougotagift.co/graphql/';

const WEBSTORE_GRAPHQL_BASE_URL =
  'https://ecomweb-stores-cmwb-ecomv2.sit.yougotagift.co';

const WEBSTORE_CDN_BASE_URL =
  'https://cdn-ecomweb-stores-cmwb-ecomv2.sit.yougotagift.co';

const ECOM_FRONTEND_BASE_URL =
  'https://ecom-frontend-ev-ecomv2.sit.yougotagift.co/shop/';

const ATWORK_BASE_URL =
  'https://atwork-frontend-aw-ecomv2.sit.yougotagift.co/';

const GREETINGS_GRAPHQL_BASE_URL =
  'https://greetings-hub-gh-ecomv2.sit.yougotagift.co/api/';

const PERSONALIZATION_GRAPHQL_BASE_URL =
  'https://ecom-personalization-ep-ecomv2.sit.yougotagift.co';

const ECOM_USERS_URL = 'https://ecom-users-eu-ecomv2.sit.yougotagift.co';

const ORDERS_GRAPHQL_BASE_URL = 'https://ecom-orders-eo-ecomv2.sit.yougotagift.co';

// #. API base url values are taking from env varaiable for sandbox and production. Not for QA.

export const defaultGGAPIBaseUrl =
  process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
    ? GROUPGIFT_GRAPHQL_BASE_URL
    : process.env.NEXT_PUBLIC_GROUPGIFT_GRAPHQL_BASE_URL;

export const webStoreAPIBaseUrl =
  process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
    ? WEBSTORE_GRAPHQL_BASE_URL
    : process.env.NEXT_PUBLIC_ECOM_GRAPHQL_BASE_URL;

export const webStoreCDNAPIBaseUrl =
  process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
    ? WEBSTORE_CDN_BASE_URL
    : process.env.NEXT_PUBLIC_WEBSTORE_CDN_BASE_URL;

export const ecomRedirectionBaseUrl =
  process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
    ? ECOM_FRONTEND_BASE_URL
    : process.env.NEXT_PUBLIC_ECOM_FRONTEND_BASE_URL;

export const atWorkRedirectionBaseUrl =
  process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
    ? ATWORK_BASE_URL
    : process.env.NEXT_PUBLIC_ATWORK_BASE_URL;

export const greetingsAPIBaseUrl =
  process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
    ? GREETINGS_GRAPHQL_BASE_URL
    : process.env.NEXT_PUBLIC_GREETINGS_GRAPHQL_BASE_URL;

export const personalizationAPIBaseUrl =
  process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
    ? PERSONALIZATION_GRAPHQL_BASE_URL
    : process.env.NEXT_PUBLIC_PERSONALIZATION_GRAPHQL_BASE_URL;

export const ecomUsersAPIUrl =
  process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
    ? ECOM_USERS_URL
    : process.env.NEXT_PUBLIC_ECOM_USERS_ENDPOINT;

export const ordersAPIBaseUrl =
  process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
    ? ORDERS_GRAPHQL_BASE_URL
    : process.env.NEXT_PUBLIC_ORDERS_GRAPHQL_BASE_URL;
