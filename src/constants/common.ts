import {
  rewardsAppRedirectUrl,
  rewardsAppRedirectUrlSA,
} from './envVariables';

// Platform Types
export enum PLATFORM_TYPE {
  WEB = 'WEB',
  TABLET = 'TABLET',
  MOBILE = 'MOBILE',
  REWARDS = 'rewards',
  ECOM = 'ECOM'
}

// pages types
export enum PAGES {
  HOME = 'Home',
  CART = 'Cart',
}

// Page URL'S
export enum PAGEURLS {
  HOME = '/',
  CART = '/cart',
  GIFT_DETAILS = '/group-gifts/details',
  GROUP_GIFTS = '/group-gifts',
  START_GROUP_GIFT = '/start-group-gift',
  ALL_BRANDS = '/brands',
  PAYMENT_SUCCESS = '/payment-success',
  PAYMENT_FAIL = '/payment-failed',
  CANCEL = '/confirm',
  GIFTS = '/group-gifts/details',
  INVITED = '/invite-friends',
  CONTRIBUTORS_LIST = '/view-contributors',
  CONFIRM = `/confirm`,
  SUSPICIOUS_ACTIVITY = '/suspicious-activity',
  OFFERS = '/offers',
  WORK = 'atwork/',
  MY_ORDERS = '/orders/ordered',
  WALLET = '/app-download',
  PROFILE = '/profile/edit',
  ACCOUNT = "/account/profile",
  CATERGORIES = "/categories",
  GAMING_GIFT_CARD = "/gaming-gift-card",
  PRIVACY_POLICY = "/privacy-policy",
  TERMS = "/terms-of-use",
}

// slider comnfig
export enum SLIDER_TITLE {
  INVITED = 'Invited',
  GIFTS = 'My Group Gifts',
}

export enum SLIDER_TYPE {
  INVITE = 'invite',
  GIFT = 'gift',
}

export enum GIFT_DETAILS_USER {
  CONTRIBUTER = 'contributer',
  ORGANIZER = 'organizer',
}

export enum GIFT_INVITE {
  LIST = 'list',
  INVITE = 'invite',
}

export enum CONTRIBUTION {
  OPEN = 'OPEN_AMOUNT',
  SUGGESTED = 'SUGGESTED_AMOUNT',
  FIXED = 'FIXED_AMOUNT',
}

// Locales
export enum LOCALES {
  ENGLISH = 'en',
  ARABIC = 'ar',
}

// Header Types
export enum HEADER_TYPE {
  MAJOR = 'MAJOR',
  MINOR = 'MINOR',
}

export enum BRAND_FILTER_TYPE {
  CATEGORY = 'categories',
  OCCASION = 'occasions',
  SEARCH = 'search',
}

export enum FOOTER_TYPE {
  HOME = 'Home',
}

export enum DATA_POINTS {
  COPY_CTA = 'COPY_CTA',
  CREATE_CTA = 'CREATE_CTA',
}

export enum MENU_TYPE{
  CAREER = "CR_ITEM_CODE"
}

export enum BUTTON_TYPE {
  INVITE = "invite",
  CONTRIBUTE = "contribute",
  SKIP = "skip",
  CONTINUE = "continue"
}

// Cognito oauth scopes
export const COGNITO_OAUTH_SCOPES = [
  'profile',
  'email',
  'openid',
  'https://www.googleapis.com/auth/user.gender.read',
  'https://www.googleapis.com/auth/user.phonenumbers.read',
  'aws.cognito.signin.user.admin',
];

// Cognito static configurations
export enum COGNITO_CONFIG {
  USER_PASSWORD_AUTH_TYPE = 'USER_PASSWORD_AUTH',
  CUSTOM_AUTH_TYPE = 'CUSTOM_AUTH',
  RESPONSE_TYPE = 'code',
  RESPONSE_SEPARATOR = '1*#*1',
}

export const GROUP_GIFT_APP_URL = 'https://app.groupgift.yougotagift.com/';
export const HELP_CENTER_URL = 'https://support.yougotagift.com/hc/en-us';
export const HELP_CENTER_URL_AR = 'https://support.yougotagift.com/hc/ar';
export const YGAG_BLOG_URL = 'https://blog.yougotagift.com/';
export const YGAG_REGION_COOKIE = 'YGAG_REGION_INFO';
export const YGAG_REGION_CODE_COOKIE = 'YGAG_REGION_CODE';
export const GUEST_USER_SESSION_COOKIE = 'session_ygag';
export const EGG_REWARDS_REFRESH_TOKEN = 'groupgift_rewards_refresh_token';
export const EGG_REWARDS_ACCESS_TOKEN = 'groupgift_rewards_access_token';
export const EGG_REWARDS_SITE_DOMIAN = 'groupgift_rewards_site_domain';
export const EGG_REFRESH_TOKEN = 'REFRESH_TOKEN';
export const LOCALE_REGION_COOKIE = 'LOCALE_REGION';
export const IS_GUEST_USER_COOKIE = 'IS_GUEST_USER';
export const JS_DELIVR_CDN =
  'https://cdn.jsdelivr.net/gh/twitter/twemoji@14.0.2/assets/';

export const REWARDS_REDIRECT_APP_URL = `${rewardsAppRedirectUrl}corporate/gift-cards/groupgift/`;
export const REWARDS_REDIRECT_APP_URL_AR = `${rewardsAppRedirectUrl}ar/corporate/gift-cards/groupgift/`;
export const REWARDS_REDIRECT_APP_URL_SA = `${rewardsAppRedirectUrlSA}corporate/gift-cards/groupgift/`;
export const REWARDS_REDIRECT_APP_URL_SA_AR = `${rewardsAppRedirectUrlSA}ar/corporate/gift-cards/groupgift/`;

// Google Tag Manager
export enum GTM_EVENTS {
  GROUP_GIFT_START = 'group_gift_start',
  GROUP_GIFT_CREATED = 'group_gift_created',
  CHECKOUT_START = 'gg_checkout_start',
  PERSONALIZE = 'gg_personalize',
  PURCHASE = 'gg_purchase',
}

export enum GTM_VALUES {
  OPEN_AMOUNT = 'Open Amount',
  SUGGESTED_AMOUNT = 'Suggested Amount',
  FIXED_AMOUNT = 'Fixed Amount',
  ON = 'On',
  OFF = 'Off',
  ORGANIZER = 'Organizer',
  CONTRIBUTER = 'Contributor',
  SKIP = 'Skip',
  SEND_AS_GIFT = 'send-as-gift',
  BUY_FOR_SELF = 'buy-for-self',
}

export enum CLEVERTAP_EVENTS {
  GROUPGIFT_START = 'GROUP GIFT START',
  GROUPGIFT_CREATED = 'GROUP GIFT CREATED',
  GG_CHECKOUT_START = 'GG CHECKOUT START',
  GG_PERSONALIZE = 'GG PERSONALIZE',
  GG_CHARGED = 'GG CHARGED',
}

export enum CLEVERTAP_PROPERTIES {
  PLATFORM = 'Platform',
  STORE = 'Store',
  BRAND_ID = 'Brand ID',
  BRAND = 'Brand',
  SETTINGS = 'Settings',
  CONTRIBUTOR_NOTE = 'Contributor Note',
  CONTRIBUTOR_TYPE = 'Contributor Type',
  COLLECTION_TARTGET = 'Collection Target',
  SHOW_AMOUNT_CONTRIBUTED = 'Show Amount Contributed',
  NO_CONTRIBUTION = 'No Contribution',
  RECEIVER_NAME = 'Receiver Name',
  RECEIVER_EMAIL = 'Receiver Email',
  RECEIVER_CONTACT = 'Receiver Contact',
  GG_ID = 'GG ID',
  AMOUNT = 'Amount',
  MEDIA = 'Media',
  PURCHASE_TYPE = 'Purchase Type',
  OCCASION = 'Occasion',
  CONTRIBUTOR_EMAIL =  'Contributor Email',
}

export enum MUTATION_TYPE {
  CREATE_CONTRIBUTION = 'createContribution',
  CREATE_GUEST_CONTRIBUTION = 'createGuestContribution',
}

export enum PERSONALISATION_TYPE {
  VIDEO = 'video',
  PHOTO = 'photo',
  GIF = 'gif',
  TEXT = 'text',
  IMAGE = 'image',
}
export enum FOOTER_ITEM_TYPE {
  ITEM_CODE_OUR_SOCIAL = "ITEM_CODE_OUR_SOCIAL",
  ITEM_CODE_OUR_SERVICES = "ITEM_CODE_OUR_SERVICES",
  ITEM_CODE_SUPPORT = "ITEM_CODE_SUPPORT",
  FB_ITEM_CODE = "FB_ITEM_CODE",
  ITEM_CODE_FOR_DEVELOPERS = "ITEM_CODE_FOR_DEVELOPERS"
}

export enum FOOTER_ALIGNMENT {
  VERTICAL = "VERTICAL",
  HORIZONTAL = "HORIZONTAL"
}