'use client';

import { Amplify } from 'aws-amplify';
import {
  identityPoolId,
  regionfromConfig,
  s3BucketName,
  userPoolId,
  userPoolWebClientId,
} from './envVariables';

const awsconfig: any = {
  Auth: {
    identityPoolId: identityPoolId,
    region: regionfromConfig,
    userPoolId: userPoolId,
    userPoolWebClientId: userPoolWebClientId,
  },
  Storage: {
    AWSS3: {
      bucket: s3BucketName,
      region: regionfromConfig,
    },
  },
  ssr: true,
};

Amplify.configure(awsconfig);

export default function ConfigureAmplifyClientSide() {
  return null;
}
