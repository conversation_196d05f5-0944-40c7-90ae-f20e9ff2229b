/**
 * @enum
 * @description Status codes of response
 */
export enum HTTP_STATUS_CODE {
  SUCCESS = 200,
  NOT_FOUND = 400,
  CORS = 401,
  FORBIDDEN = 403,
  INVALID_OR_NOT_FOUND = 404,
  LIMIT_EXCEED = 406,
  SCRIPT_INJECTION = 422,
  UNUSUAL_ACTIVITY = 603,
  OUTBOUND_CALL = 604,
  INVALID_STORE = 605,
  INVALID_USER_ENTITY = 609,
  INVALID_ACTION = 610,
  INVALID_BRAND = 2003,
  DENOMINATION_UNAVAILABLE = 2008,
  UNSUPPORTED_PLATFORM = 2009,
  SESSION_EXPIRED = 2010,
  INVALID_CART_ITEM = 2011,
  OTP_BASE_ERROR = 24000,
  OTP_VERIFY = 24002,
  OTP_INVALID = 24004,
  OTP_EXPIRED = 24006,
  OTP_MAXIMUM_ATTEMPT_REACHED = 24008,
}

export const PAGE_404_REDIRECT = {
  redirect: {
    destination: "/error/404/",
    permanent: false,
  },
};

export const CUSTOM_404_REDIRECT = {
  redirect: {
    destination: "/custom-error",
    permanent: false,
  },
};
