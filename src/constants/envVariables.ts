export const imageBaseUrl =
  process.env.NEXT_PUBLIC_GROUPGIFT_ASSET_PREFIX || '';

const ECOM_AUTH_REDIRECT_URL =
  'https://ecom-auth-frontend-ev-ecomv2.sit.yougotagift.co';

const GOOGLE_TAG_MANAGER_ID = 'GTM-MKF5ZQ6';
const CLEVERTAPID = 'TEST-75R-466-566Z';
const GIPHY_API_KEY = 'k0dkAJ8P3Ex5wva4Od4I7hWdZkvipbgk';
const HOTJARID = '4965648';
const LOCALE_REGION_COOKIE_DOMAIN = '.yougotagift.co';
const EGG_FE_BASE_URL =
  'https://ecom-groupgift-frontend-egg-ecomv2.sit.yougotagift.co';
const REWARDS_APP_URL = 'https://rewards-rew-1401.sit.yougotagift.co/';
const REWARDS_APP_HOME_URL =
  'https://rewards-rew-1401.sit.yougotagift.co/corporate/gift-cards/';
const ECOM_APP_URL = 'https://ecom-frontend-do-ae.sit.yougotagift.co/shop'
const AT_WORK_APP_URL = "https://atwork-frontend-aw-ecomv2.sit.yougotagift.co/";

export const localeBaseUrl =
  process.env.NODE_ENV === 'production'
    ? process.env.NEXT_PUBLIC_GROUPGIFT_ASSET_PREFIX
    : '../../public';

export const redirectURL =
  process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
    ? ECOM_AUTH_REDIRECT_URL
    : process.env.NEXT_PUBLIC_ECOM_AUTH_REDIRECT_URL;
export const GTM_ID =
  process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
    ? GOOGLE_TAG_MANAGER_ID
    : process.env.NEXT_PUBLIC_GOOGLE_TAG_MANAGER_ID;
export const HOTJAR_ID =
  process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
    ? HOTJARID
    : process.env.NEXT_PUBLIC_HOTJAR_ID;
export const CLEVERTAP_ACCOUNT_ID =
  process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
    ? CLEVERTAPID
    : process.env.NEXT_PUBLIC_CLEVERTAP_ID;

export const GIPHY_KEY =
  process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
    ? GIPHY_API_KEY
    : process.env.NEXT_PUBLIC_GIPHY_API_KEY;

export const DOMAIN =
  process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
    ? LOCALE_REGION_COOKIE_DOMAIN
    : process.env.NEXT_PUBLIC_LOCALE_REGION_COOKIE_DOMAIN;

export const eggFEUrl =
  process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
    ? EGG_FE_BASE_URL
    : process.env.NEXT_PUBLIC_EGG_FRONTEND_BASE_URL;

export const rewardsAppRedirectUrl: any =
  process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
    ? REWARDS_APP_URL
    : process.env.NEXT_PUBLIC_REWARDS_APP_URL;

export const rewardsAppRedirectUrlSA: any =
  process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
    ? REWARDS_APP_URL
    : process.env.NEXT_PUBLIC_REWARDS_APP_SA_HOME_URL;

export const rewardsAppHomeUrl: any =
  process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
    ? REWARDS_APP_HOME_URL
    : process.env.NEXT_PUBLIC_REWARDS_APP_HOME_URL;

export const rewardsAppHomeUrlSA: any =
  process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
    ? REWARDS_APP_HOME_URL
    : process.env.NEXT_PUBLIC_REWARDS_APP_SA_HOME_URL;

export const ecomAppUrl: any =
  process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
    ? ECOM_APP_URL
    : process.env.NEXT_PUBLIC_ECOM_APP_URL;

export const atWorkUrl: any =
  process.env.NEXT_PUBLIC_ENVIRONMENT === 'qa'
    ? AT_WORK_APP_URL
    : process.env.NEXT_PUBLIC_AT_WORK_APP_URL;

export const solutionsHubUrl = process.env.NEXT_PUBLIC_SOLUTIONS_HUB_URL;

// #. AWS Config
export const identityPoolId =
  process.env.NEXT_PUBLIC_ECOM_COGNITO_IDENTITY_POOL_ID;
export const regionfromConfig = process.env.NEXT_PUBLIC_ECOM_COGNITO_REGION;
export const userPoolId = process.env.NEXT_PUBLIC_ECOM_COGNITO_USER_POOL_ID;
export const userPoolWebClientId =
  process.env.NEXT_PUBLIC_ECOM_COGNITO_USER_POOL_WEBCLIENT_ID;
export const s3BucketName = process.env.NEXT_PUBLIC_ECOM_COGNITO_S3_BUCKET_NAME;
export const domain = process.env.NEXT_PUBLIC_ECOM_COGNITO_OAUTH_DOMAIN;
