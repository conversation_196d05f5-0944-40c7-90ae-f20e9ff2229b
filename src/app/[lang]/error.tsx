'use client'; // Error components must be Client Components

import Image from 'next/image';
import { useEffect, useState } from 'react';
import * as Sentry from '@sentry/nextjs';
import { imageBaseUrl } from '@/constants/envVariables';
import Loader from '@/features/common/loader/Loader';
import { useSearchParams } from 'next/navigation';

export default function Error({
  error,
  reset,
}: Readonly<{
  error: Error & { digest?: string };
  reset: () => void;
}>) {
  useEffect(() => {
    // Log the error to Sentry
    Sentry.captureException(error);
  }, [error]);

  // #. Set current year
  const date = new Date();
  const searchParams = useSearchParams();
  let year = date.getFullYear();
  const [showError, setShowError] = useState(false);
  const isRewards = searchParams.get('embedded');

  const ygagLogo = `${imageBaseUrl}/images/ygag-logo.png`;

  // #. useEffect to delay the visibility of the error page
  useEffect(() => {
    const timer = setTimeout(() => {
      setShowError(true);
    }, 2000);

    // Cleanup the timer
    return () => clearTimeout(timer);
  }, []);

  if (!showError) {
    return <Loader />;
  }

  return (
    <div>
      <div className="back_Soon">
        {!isRewards && (
          <header>
            <div className={'back_Soon_header'}>
              <a href={`/`}>
                <img src={ygagLogo} width={73} height={50} alt="YOUGotaGift" />
              </a>
            </div>
          </header>
        )}

        <section
          className={`container ${'back_Soon_main-section'}
        ${isRewards ? 'back_Soon_main-section--rew' : ''}
        `}
        >
          <div>
            <h2>Hang on to your hats!</h2>
            <h5>We’re giving our systems a refresh</h5>
            <div>
              <img
                src={`${imageBaseUrl}/images/icons/error.svg`}
                width={135}
                height={164}
                alt="error"
              />
            </div>
            <p>
              Sorry for the temporary inconvenience, we’ll be up and running
              again before you know it!
            </p>
          </div>
        </section>

        {!isRewards && (
          <footer className={'back_Soon_footer'}>
            <div className={`container ${'container'}`}>
              <div>
                <p>©{year} YOUGotaGift.com Ltd. All rights reserved.</p>
              </div>
              <div>
                <ul>
                  <li>
                    Available in UAE <span>|</span> Saudi Arabia <span>|</span>{' '}
                    Oman <span>|</span> Bahrain <span>|</span> Qatar{' '}
                    <span>|</span> Lebanon <span>|</span> India <span>|</span>{' '}
                    Africa <span>|</span> Europe &amp; USA.
                  </li>
                </ul>
              </div>
            </div>
          </footer>
        )}
      </div>
    </div>
  );
}
