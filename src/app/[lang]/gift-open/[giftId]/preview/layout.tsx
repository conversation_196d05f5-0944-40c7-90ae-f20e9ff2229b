import { IS_GUEST_USER_COOKIE } from '@/constants/common';
import type { Metadata } from 'next';
import AuthenticationProviderServer from '@/features/common/auth/AuthenticationProviderServer';
import { getUserInfo } from '@/utils/authInfo';
import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import getCookie from '@/utils/getCookie';

export const metadata: Metadata = {
  title: 'YOUGotaGift.com | Group Gifts',
  description: 'YOUGotaGift.com Group Gifting - The easiest way to organise a Group Gift and invite your friends to contribute!',
  keywords: 'gift,gifts,gifting,group gifting,weddings,birthdays,baby showers,wedding registry,group raise money'
};

export default async function GiftsPreviewLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const cookieStore = cookies();
  const guestLogin = cookieStore.get(IS_GUEST_USER_COOKIE);
    const guestRefereshTokenCookie: any = getCookie('GROUPGIFT_REFRESH_TOKEN');
  // #. Get token info API (server side) call
  const userInfo = await getUserInfo();
  // #.Redirect to home page, if user is not signed in or guest user.
  if (guestLogin?.value !== 'true' && !userInfo.isUserSignedIn) {
    redirect('/');
  }

  const guestTokens = {
    refereshToken: guestRefereshTokenCookie?.value || '',
    isGuestLoggedIn: guestLogin?.value || '',
  };

  return (
    <>
      <AuthenticationProviderServer>
        <div>
          <section>{children}</section>
        </div>
      </AuthenticationProviderServer>
    </>
  );
}
