'use client';
import GiftOpenPageLayout from '@/features/gifts/giftsOpen/giftOpenPageLayout';
import useGiftOpenSlice from '@/features/gifts/giftsOpen/giftOpenSlice';
import { useAppSelector } from '@/redux/hooks';
import React, { useEffect, useState } from 'react';
import { GIFT_OPEN_SECTION } from '@/features/gifts/giftsOpen/constants/constants';
import OpeningIntroductionGg from '@/features/gifts/giftsOpen/openingIntroductionGG/OpeningIntroductionGG';
import StoriesDisplay from '@/features/gifts/giftsOpen/storiesDisplay/StoriesDisplay';
import StoryFooterGroupGift from '@/features/gifts/giftsOpen/openingIntroductionGG/StoryFooterGroupGift';
import GiftUnwrap from '@/features/gifts/giftsOpen/detailsDisplay/giftUnwrap/GiftUnwrap';
import GiftCardDetails from '@/features/gifts/giftsOpen/detailsDisplay/giftCardDetails/GiftCardDetails';
import useContributionSlice from '@/features/gifts/contributionFlowSlice';
import useCommonSlice from '@/features/common/commonSlice';
import { useRouter } from 'next/navigation';
import useGuestUserSlice from '@/features/gifts/giftsOpen/guestUserSlice';

const Page = () => {
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // #. get storage values
  const {
    getRecieverInfo,
    getPersonalisation,
    getAmount,
    getCurrency,
    getBrandImage,
    getBrandName,
  } = useContributionSlice();
  const { getGuestUserName } = useGuestUserSlice();

  const recieverData: any = useAppSelector(getRecieverInfo);
  const brandImage: any = useAppSelector(getBrandImage);
  const brandName: any = useAppSelector(getBrandName);
  const currency: any = useAppSelector(getCurrency);
  const amount: any = useAppSelector(getAmount);
  const contributingName: any = useAppSelector(getGuestUserName);

  // #. Get active section name from the slice data
  const { getActiveSection } = useGiftOpenSlice();
  const activeSection = useAppSelector(getActiveSection);

  const { getTokenInfo } = useCommonSlice();
  const { userAttributes }: any = useAppSelector(getTokenInfo);
  const router = useRouter();

  useEffect(() => {
    // #. back to home if  required data is not available
    if (!recieverData?.name || !brandName) {
      router.push('/');
    }
  }, [recieverData?.name, brandName]);

  const giftPreviewData = {
    recipientName: recieverData?.name,
    recipientEmail: recieverData?.email,
    organiserName: recieverData?.organizerName,
    cardSkinImage: brandImage,
    cardName: brandName,
    cardAmount: currency + ' ' + amount,
    contributors: {
      edges: [
        {
          node: {
            name: contributingName?.length? contributingName : userAttributes?.name,
            profileImg: userAttributes?.picture,
            personalizationCount: 0,
          },
        },
      ],
    },
  };

  return (
    <GiftOpenPageLayout isLoading={isLoading}>
      {activeSection === GIFT_OPEN_SECTION.OPEN && (
        <OpeningIntroductionGg {...giftPreviewData} />
      )}

      {activeSection === GIFT_OPEN_SECTION.STORIES && (
        <>
          <div className="story-area">
            <StoriesDisplay setIsLoading={setIsLoading} />
          </div>
          {/* Removed as we no longer need footer for open experience preview as per new designs */}
          {/* <StoryFooterGroupGift />{' '} */}
        </>
      )}

      {activeSection === GIFT_OPEN_SECTION.UNWRAP && <GiftUnwrap />}

      {activeSection === GIFT_OPEN_SECTION.GIFT && (
        <GiftCardDetails groupGiftData={giftPreviewData} />
      )}
    </GiftOpenPageLayout>
  );
};

export default Page;
