'use client';
import React from 'react';
import { imageBaseUrl } from '@/constants/envVariables';
import { useTranslation, Trans } from 'react-i18next';
import { HELP_CENTER_URL, HELP_CENTER_URL_AR } from '@/constants/common';
import getRegionAndLocale from '@/utils/getRegionAndLocale';
import { useRouter } from 'next/navigation';
import Button from '@/features/common/button/Button';

const page = () => {
  // translations
  const { t } = useTranslation();
  const { locale } = getRegionAndLocale();
  const router = useRouter();

  const suspiciousImg = `${imageBaseUrl}/images/icons/suspicious.webp`

  const HELPLINE_URL = locale === 'ar' ? HELP_CENTER_URL_AR : HELP_CENTER_URL;

  /**
   * @method handleRedirect
   * @description handle redirect to helpline
   */
  const handleRedirect = () => {
    router.push(HELPLINE_URL, undefined);
  };

  return (
    <div className="error-country">
      <div className="error-country__left error-country__contents">
        <div className="suspended-contents">
          <div className="suspended-contents__header">
            <h3>
              <Trans i18nKey="accountSuspended" />
            </h3>
          </div>
          <div className="suspended-contents__info">
            <p>{t('suspendedInfo2')}</p>
          </div>
          <img src={suspiciousImg} alt='suspicious' className='suspended-contents__img' />
          <div className="suspended-contents__reactivate">
            <Button
              theme="primary"
              className={'gg-button'}
              arrow="arrow-forward"
              action={handleRedirect}
            >
              {t('reactivateAccount')}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default page;
