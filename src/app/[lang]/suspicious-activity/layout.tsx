import '@/styles/style.scss';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'YOUGotaGift.com | Group Gifts',
  description:
    'YOUGotaGift.com Group Gifting - The easiest way to organise a Group Gift and invite your friends to contribute!',
  keywords:
    'gift,gifts,gifting,group gifting,weddings,birthdays,baby showers,wedding registry,group raise money',
};

export default async function NotFoundPageLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div className="suspicious-activity">
      <section className="suspicious-activity__container">{children}</section>
    </div>
  );
}
