@import '@/styles/abstracts/abstracts';

.guest {
  &__wrap {
    button {
      width: 100%;
      height: 50px;
      font-weight: 500;
      font-family: var(--font-mona-sans);
      font-size: rem(16);
      border-radius: 8px;
      padding: 0;
    }
  }

  &__details {
    h4 {
      margin-bottom: rem(32);
    }

    label {
      font-size: rem(12);
      color: $text-grey;

      @include rtl-styles {
        right: 0;
      }
    }

    input {
      font-size: rem(16);
      color: $dark-charcoal;
      font-weight: 600;
      line-height: 24px;
    }
  }

  .contribute-btn{
    margin-top: 8px;
    button{
      font-size: 16px;
      font-weight: 700;
      line-height: 24px;
      letter-spacing: -0.16px;
    }
 
  }

  &__seperator {
    text-align: center;
    display: block;
    font-size: rem(16);
    margin: rem(24) 0;
    color: $dark-charcoal;
    font-weight: 700;
    line-height: 24px;
    letter-spacing: -0.16px;
  }

  &__login {
    background-color: rgba(210, 201, 255, 0.25);
    border-radius: rem(12);
    padding: rem(8) rem(16);

    p {
      color: $dark-charcoal;
      font-size: rem(14);
      font-weight: 500;
      margin-bottom: rem(16);
      line-height: 18px;
      letter-spacing: -0.14px;
    }

    button {
      font-weight: 600;
    }
  }

  .download-app {
    margin-top: 24px;
    position: absolute;
    left: 0;
    padding-inline: 20px;
    bottom: 32px;
    width: 100%;
    p {
      text-align: center;
      font-size: rem(14);
      font-weight: 600;
      margin-bottom: 16px;
      line-height: 24px;
      letter-spacing: -0.14px;
    }
    &__image-container {
      display: flex;
      justify-content: center;
      margin-bottom: rem(16);
      flex-wrap: wrap;
      gap: 16px;

      img {
        height: rem(48);
        width: rem(48);
        cursor: pointer;
        border-radius: 50%;
      }
    }
    &__guest-test {
      background-color: $medium-light-grey;
      padding: 8px;
      border-radius: 12px;
      p {
        font-size: rem(12);
        margin: 0;
        color: $dark-charcoal;
        font-weight: 500;
        line-height: 18px;
        letter-spacing: -0.12px;
      }
    }
  }
}
