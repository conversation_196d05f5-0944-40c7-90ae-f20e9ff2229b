import type { Metadata } from 'next';
import { imageBaseUrl } from '@/constants/envVariables';
import AuthenticationProviderServer from '@/features/common/auth/AuthenticationProviderServer';

export const metadata: Metadata = {
  title: 'YOUGotaGift.com | Group Gifts',
  description: 'YOUGotaGift.com Group Gifting - The easiest way to organise a Group Gift and invite your friends to contribute!',
  keywords: 'gift,gifts,gifting,group gifting,weddings,birthdays,baby showers,wedding registry,group raise money'
};

export default async function GiftsLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // Authentication is now handled by the server component
  return (
    <>
      <AuthenticationProviderServer>
        <div
          className="gift-details-wrapper gift-details-bg"
          style={{
            backgroundImage: `url(${imageBaseUrl}/images/gift-wrapper.jpg)`,
            backgroundRepeat: 'no-repeat',
            backgroundSize: 'cover',
          }}
        >
          <div className={`gift-details`}>
            <section>{children}</section>
          </div>
        </div>
      </AuthenticationProviderServer>
    </>
  );
}
