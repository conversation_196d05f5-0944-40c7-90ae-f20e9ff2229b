'use client';

import React, { useEffect, useState } from 'react';
import ContributionHeader from '@/features/common/header/contributionHeader/ContributionHeader';
import styles from './style.module.scss';
import Button from '@/features/common/button/Button';
import { TextField } from '@mui/material';
import { usePara<PERSON>, useRouter } from 'next/navigation';
import useGiftsAPI from '@/features/gifts/giftsAPI';
import { IS_GUEST_USER_COOKIE, PLATFORM_TYPE } from '@/constants/common';
import { setCookie } from '@/utils/getAndsetCookie';
import { PAGEURLS } from '@/features/gifts/constants/gifts.constants';
import useCommonSlice from '@/features/common/commonSlice';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import { redirectURL } from '@/constants/envVariables';
import getRegionAndLocale from '@/utils/getRegionAndLocale';
import { format } from 'url';
import { HAS_WINDOWS_REF } from '@/utils/hasWindowRef';
import { useTranslation } from 'react-i18next';
import { isValidEmailAddress } from '@/utils/emailValidation';
import Notifier from '@/features/common/notifier/Notifier';
import GuestLoginSkeleton from '@/features/gifts/contentLoader/guestLoginSkeleton/GuestLoginSkeleton';
import sanitizeHTML from '@/utils/sanitizeHTML';
import { nl2br } from '@/utils/nl2br';
import useGuestUserSlice from '@/features/gifts/giftsOpen/guestUserSlice';
import TamperedError from '@/features/common/errorPage/TamperedError';
import Loader from '@/features/common/loader/Loader';
import useHomeAPI from '@/features/home/<USER>';

/**
 * Functional component for guest login form.
 * @returns JSX.Element
 */
const GuestLogin = (): JSX.Element => {
  const dispatch = useAppDispatch();
  const { t } = useTranslation();
  const params = useParams();
  const router = useRouter();
  const { locale, region } = getRegionAndLocale();
  const { FetchGuestTokens } = useGiftsAPI();
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [customError, setCustomError] = useState<boolean>(false);
  const [nameValidate, setNameValidate] = useState('');
  const [invalidEmail, setInvalidEmail] = useState('');
  const { setGuestUserName } = useGuestUserSlice();
  const { getTokenInfo } = useCommonSlice();
  const { AccessToken, isUserSignedIn }: any = useAppSelector(getTokenInfo);
  const { FetchStoreLanguages, GuestTokenValidity } = useGiftsAPI();
  const { storeConfigData } = FetchStoreLanguages(region);
  const languages =
    storeConfigData?.siteConfigs?.edges[0]?.node?.languages.length > 1;

  const notifierDispatch = useAppDispatch();
  const { setNotifierState } = useCommonSlice();

  // #. Set invalid email message
  const invalidEmailMessage = t('invalidEmailAddress');

  const handleNameChange = (event: any) => {
    const name = String(event?.target?.value).trimStart().replace(/[<>]/g, '');
    setName(name);

    const isValidName = name && name.length >= 2;
    setNameValidate(
      !name.length
        ? t('mandatoryError')
        : !isValidName
        ? t('FieldErrorMsg')
        : ''
    );
  };

  const handleEmailChange = (event: any) => {
    const email = String(event?.target?.value)
      ?.trimStart()
      ?.replace(/[<>]/g, '');
    setEmail(email);

    const isValidEmail = isValidEmailAddress(email) && email.length <= 250;
    const message = isValidEmail ? '' : invalidEmailMessage;
    setInvalidEmail(event?.target?.value?.length > 0 ? message : '');
  };

  const inputParams: any = {
    accessToken: params?.token1,
    verifyToken: params?.token2,
    name: name,
    email: email,
    onError: (error: any) => {
      setCustomError(true);
      onErrorOccured(error);
    },
    onSuccessGuestLogin: (value: any) => {
      setCustomError(false);
      onSuccessGuestLogin(value);
    },
    onSuccessInvited: (value: any) => {
      setCustomError(false);
      onSuccessInvited(value);
    },
    userToken: AccessToken || '',
  };

  /**
   * @method onErrorOccured
   */
  const onErrorOccured = (message: string) => {
    // #. Show the notifier
    notifierDispatch(
      setNotifierState({
        title: message,
        icon: 'failerIcon',
      })
    );
  };
  const onSuccessGuestLogin = (resData: any) => {
    setCookie(IS_GUEST_USER_COOKIE, 'true', 30);
  };

  /**
   * @method onSuccessInvited
   * @param {any} resData
   */
  const onSuccessInvited = (resData: any) => {
    //# Redirection to gifts contribute page
    router.push(`${PAGEURLS.GIFTS_DETAILS}/${resData?.data?.referenceId}`);
  };

  /**
   * @method handleContributeClick
   */
  const handleContributeClick = () => {
    dispatch(setGuestUserName(name));
    FetchGuestTokens(inputParams);
  };

  /* The code block is defining a function `handleLogin` that is called when a button is clicked. */
  const currentURL = HAS_WINDOWS_REF && window?.location;
  const handleLogin = () => {
    if (currentURL) {
      const url = format({
        pathname: `${redirectURL}/${locale}/login/`,
        query: {
          rdir: `${currentURL.origin.toString()}${currentURL?.pathname.toString()}`,
          platform: PLATFORM_TYPE.WEB.toLowerCase(),
          store: `${locale}-${region}`,
          guest: true,
        },
      });
      router.push(url, undefined);
    }
  };

  //Download app data getting from webstore API
  const { useDownloadApp } = useHomeAPI();
  const { data } = useDownloadApp();

  // #.Set app store images
  const appBanner = data?.downloadApp[0]?.roundedAppBanner;

  // #. getting Token expired or not
  const {
    data: guestData,
    error: guestError,
    loading: guestLoading,
  } = GuestTokenValidity(inputParams?.accessToken, inputParams?.verifyToken);

  useEffect(() => {
    if (isUserSignedIn && guestData) {
      setCookie(IS_GUEST_USER_COOKIE, 'false', 30);
      FetchGuestTokens(inputParams);
    }
  }, [isUserSignedIn, guestData]);

  return (
    <>
      {isUserSignedIn === undefined || (isUserSignedIn && guestLoading) ? (
        <Loader />
      ) : (
        <>
          {!guestLoading && guestData && !isUserSignedIn ? (
            <div className={`${styles['guest']} guest`}>
              <ContributionHeader languages={languages} />
              <div className={styles['guest__wrap']}>
                <div className={styles['guest__details']}>
                  <h4>{t('enterDetails')}</h4>
                  <TextField
                    id="Name"
                    className={'name-feild'}
                    placeholder={t('name')}
                    variant="outlined"
                    value={name}
                    autoFocus
                    helperText={nameValidate}
                    onChange={handleNameChange}
                    inputProps={{ maxLength: 26 }}
                    // onBlur={handleNameChange}
                  />
                  <TextField
                    id="Email"
                    className={'email-feild'}
                    placeholder={t('email')}
                    variant="outlined"
                    value={email}
                    onChange={handleEmailChange}
                    onBlur={handleEmailChange}
                    helperText={invalidEmail}
                  />
                </div>
                <div className={styles['contribute-btn']}>
                <Button
                  theme="primary"
                  action={handleContributeClick}
                  attribues={{
                    disabled:
                      name && email && !nameValidate && !invalidEmail
                        ? false
                        : true,
                  }}
                >
                  {t('contributeGuest')}
                </Button>
                </div>
                <span className={styles['guest__seperator']}>{t('or')}</span>
                <div className={styles['guest__login']}>
                  <p
                    dangerouslySetInnerHTML={{
                      __html: sanitizeHTML(
                        nl2br(
                          t('loginText', {
                            name: guestData?.invitationDetail?.title,
                          })
                        ),
                        {
                          ALLOWED_TAGS: ['/'],
                        }
                      ),
                    }}
                  ></p>
                  <Button
                    theme="secondary"
                    action={handleLogin}
                  >
                    {t('loginSignUp')}
                  </Button>
                </div>

                <div className={styles['download-app']}>
                  <p>{t('downloadApp')}</p>
                  <div className={styles['download-app__image-container']}>
                    {appBanner?.map((item: any) => (
                      <a href={item?.itemUrl} key={item?.itemName}>
                        <img src={item?.itemImage} alt={item?.itemName} />
                      </a>
                    ))}
                  </div>
                  <div className={styles['download-app__guest-test']}>
                    <p>{t('guestDescp')}</p>
                  </div>
                </div>
              </div>
              <Notifier />
            </div>
          ) : (
            !guestData && !guestError && <GuestLoginSkeleton />
          )}
          {(guestError || customError) && <TamperedError />}
        </>
      )}
      <Notifier />
    </>
  );
};

export default GuestLogin;
