'use client';
import React, { useEffect, useState } from 'react';
import styles from './Confirm.module.scss';
import Image from 'next/image';
import ConfirmPreview from '@/features/gifts/confirm/confirmPreview/ConfirmPreview';
import {
  CONFIRM_TYPES,
  PAGEURLS,
  STATUS_INFO,
} from '@/features/gifts/constants/gifts.constants';
import { useTranslation } from 'react-i18next';
import useContributionSlice from '@/features/gifts/contributionFlowSlice';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import { imageBaseUrl } from '@/constants/envVariables';
import { useParams, useRouter } from 'next/navigation';
import useGiftsAPI from '@/features/gifts/giftsAPI';
import useCommonSlice from '@/features/common/commonSlice';
import getRegionAndLocale from '@/utils/getRegionAndLocale';
import Notifier from '@/features/common/notifier/Notifier';
import GiftConfirmSkeleton from '@/features/gifts/contentLoader/giftConfirmSkeleton/GiftConfirmSkeleton';
import { getCookie } from '@/utils/getAndsetCookie';
import { IS_GUEST_USER_COOKIE } from '@/constants/common';
import useGuestUserSlice from '@/features/gifts/giftsOpen/guestUserSlice';
import TamperedError from '@/features/common/errorPage/TamperedError';
import ConfirmRecieverDetails from '@/features/gifts/confirm/confirmRecieverDetails/ConfirmRecieverDetails';
import ContributionHeader from '@/features/common/header/contributionHeader/ContributionHeader';
import SvgIcon from '@/features/common/svgIcon/SvgIcon';
import Link from 'next/link';
import Marquee from 'react-fast-marquee';
import CustomLink from '@/features/common/customLink/CustomLink';

const Confirm = () => {
  // translations
  const { t } = useTranslation();
  const router = useRouter();
  const routeParams = useParams();
  const { locale, region } = getRegionAndLocale();

  // taking public image config url
  const backButton = `${imageBaseUrl}/images/icons/arrow-back.svg`;
  const errorIcon = `${imageBaseUrl}/images/icons/error-icon.svg`;

  const [reciever, setReciever] = useState<any>();
  const [brandData, setBrandData] = useState<any>({});
  const [personalisationData, setPersonalisationData] = useState<any>({});
  const [loading, setLoading] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [guestName, setGuestName] = useState<any>();

  const personalisationSlice = useAppSelector(
    (state) => state.contributionFlow
  );
  const { onPersonalisationCreated } = useGiftsAPI();
  const { getTokenInfo, getGuestInfo } = useCommonSlice();
  const { getGuestUserName } = useGuestUserSlice();
  const { AccessToken }: any = useAppSelector(getTokenInfo);
  const { GuestAccessToken }: any = useAppSelector(getGuestInfo);
  const isGuestLoginCookie: any = getCookie(IS_GUEST_USER_COOKIE);
  const guestUserName: any = useAppSelector(getGuestUserName);
  const notifierDispatch = useAppDispatch();
  const { setNotifierState } = useCommonSlice();
  const { FetchGGStatus } = useGiftsAPI();

  // #. Api for Checking the gift status
  const { data: giftDetailData, loading: giftsLoading }: any = FetchGGStatus(
    routeParams?.giftid,
    locale,
    isGuestLoginCookie === 'true' ? GuestAccessToken : AccessToken,
    isGuestLoginCookie
  );

  const giftStatus = giftDetailData?.groupGiftDetail?.statusInfo?.status;
  const contributingName = giftDetailData?.groupGiftDetail?.contributingAs;

  /**
   * @method onErrorOccured
   */
  const onErrorOccured = (message: string, title?: string) => {
    // #. Show the notifier
    notifierDispatch(
      setNotifierState({
        title: message || t('errorTitle'),
        icon: 'infoIcon',
      })
    );
  };

  // #. handle contribution continue redirection
  const handleContritube = () => {
    setLoading(true);
    if (isPersonalisationSkipped && brandData?.amount === 0) {
      onErrorOccured(t('errorDesc'), t('noContribute'));
      setLoading(false);
    } else {
      onPersonalisationCreated(params);
    }
  };

  const params: any = {
    personalisationSlice: personalisationSlice,
    tokenInfo: isGuestLoginCookie === 'true' ? GuestAccessToken : AccessToken,
    locale: locale,
    onError: (error: any) => {
      onErrorOccured(error);
      setLoading(false);
    },
    onSuccess: (value: boolean) => {
      setLoading(value);
    },
    isGuestLoginCookie,
    guestName: guestName?.length ? guestName : contributingName,
  };

  /**
   * @method isEmpty
   * @param {any} obj
   * @returns The function `isEmpty` returns a boolean value
   */
  const isEmpty = (obj: any) => {
    for (let key in obj) {
      if (obj.hasOwnProperty(key)) {
        return true;
      }
    }
    return false;
  };

  // #. get storage values
  const {
    getRecieverInfo,
    getMessage,
    getPersonalisation,
    getOccasion,
    getAmount,
    getCurrency,
    getBrandImage,
    getBrandName,
  } = useContributionSlice();
  const recieverData: any = useAppSelector(getRecieverInfo);
  const brandImage: any = useAppSelector(getBrandImage);
  const brandName: any = useAppSelector(getBrandName);
  const currency: any = useAppSelector(getCurrency);
  const amount: any = useAppSelector(getAmount);
  const personalisation: any = useAppSelector(getPersonalisation);
  const message: any = useAppSelector(getMessage);
  const occasion: any = useAppSelector(getOccasion);

  // # check personalisation skipped
  const isPersonalisationSkipped: boolean =
    personalisationData?.occasion?.greetinghubImageUrl?.length > 0 ||
    personalisationData?.message?.message?.length > 0 ||
    isEmpty(personalisationData?.personalisation?.gif) ||
    isEmpty(personalisationData?.personalisation?.video) ||
    personalisationData?.personalisation?.photo?.file?.length > 0
      ? false
      : true;

  // #. Handle router back redirection
  const goBack = () => {
    router.replace(
      `${PAGEURLS.GROUP_GIFTS}/${routeParams?.giftid}${PAGEURLS.MESSAGE}`
    );
  };

  useEffect(() => {
    setReciever(recieverData);
  }, [recieverData]);

  useEffect(() => {
    setBrandData({
      brandName: brandName,
      brandImage: brandImage,
      currency: currency,
      amount: amount,
    });
  }, [brandName, brandImage, currency, amount]);

  useEffect(() => {
    setPersonalisationData({
      personalisation: personalisation,
      message: message,
      occasion: occasion,
    });
  }, [personalisation, message, occasion]);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    setGuestName(guestUserName);
  }, [guestUserName]);

  return (
    <>
    <ContributionHeader />
      {isLoading || giftsLoading ? (
        <GiftConfirmSkeleton />
      ) : giftStatus === STATUS_INFO.OPEN || giftStatus === undefined ? (
        <div className={styles['confirm']} id="drawer-container">
          <div className={styles['confirm__progress']}>
            <div className={styles['confirm__blk']}>
            <span onClick={goBack}>
              <img src={backButton} alt="img" />
            </span>

            <h4 className={styles['confirm__title']}>{t('confirm')}</h4>
            </div>
           {!isPersonalisationSkipped && <div className={styles['preview-icon']}>
            <CustomLink
              hrefLink={`${PAGEURLS.GIFT_OPEN}/${routeParams?.giftid}${PAGEURLS.PREVIEW}/`}
              key={routeParams?.giftid?.toString()}
            >
              <SvgIcon icon="preview-icon" width="32" height="32" />
            </CustomLink>
            </div>}
          </div>
          <ConfirmPreview
            type={
              isPersonalisationSkipped
                ? CONFIRM_TYPES.SKIPPED
                : CONFIRM_TYPES.PREVIEW
            }
            personalisationData={personalisationData}
          />
          <div className={styles['gift-wrapper']}>
            <div className={styles['gift-details']}>
              <h5>
                <span className={styles['gift-text']}>{t('giftingTo')}</span>
                {reciever?.name.length > 16 ? <Marquee
                    speed={30}
                    className={`${styles['marquee']}`}
                    direction={locale === 'ar' ? 'right' : 'left'}
                  >
                    <span>{reciever?.email}</span>
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  </Marquee> : reciever?.name}
              </h5>
              <p>
                {t('organizedByText')}
                &nbsp;<span>{reciever?.organizerName}</span>
              </p>

              <div className={styles['gift-details__happy-card']}>
                <div className={styles['gift-details__happy-card-image']}>
                  <Image
                    src={brandData?.brandImage}
                    width={104}
                    height={67}
                    alt="happy-card"
                  />
                </div>
                <div className={styles['gift-details__happy-card-title']}>
                  <p>{brandData?.brandName}</p>
                  {brandData?.amount > 0 && (
                    <p
                      className={styles['gift-details__happy-card-title-text']}
                    >
                      {brandData?.currency} {brandData?.amount}
                    </p>
                  )}
                </div>
              </div>
            </div>
            <ConfirmRecieverDetails
              reciever={reciever}
              loading={loading}
              handleContritube={handleContritube}
              contributingName={
                guestName?.length ? guestName : contributingName || ''
              }
            />
          </div>
          <Notifier />
        </div>
      ) : (
        <TamperedError />
      )}
    </>
  );
};

export default Confirm;
