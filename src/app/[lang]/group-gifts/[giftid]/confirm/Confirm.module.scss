@import '@/styles/abstracts/abstracts';

.confirm {

  &__blk{
    display: flex;
    align-items: center;
    gap: 8px;
  }
  &__progress {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    justify-content: space-between;

    span {
      height: 32px;
      display: flex;
      align-items: center;
      cursor: pointer;
    }
    h4 {
      font-family: var(--font-bricolage); font-optical-sizing: none;
      font-size: 32px;
      font-weight: 800;
      line-height: 32px;
      letter-spacing: -0.16px;
      color: $dark-charcoal;
    }
    img {
      display: block;
      @include rtl-rotate;
    }

    .preview-icon{
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: $medium-light-grey;
      border-radius: 50%;
      @include rtl-rotate;

      >a{
        display: contents;
      }
    }
  }
}

.gift-wrapper {
  -webkit-backdrop-filter: blur(20px) !important;
  backdrop-filter: blur(20px) !important;
  box-shadow: 0px -4px 20px 0px rgba(0, 0, 0, 0.06) !important;
  background-color: rgba(255, 255, 255, 0.95) !important;
  padding: rem(16) rem(20);
  border-top-left-radius: 20px;
  border-top-right-radius: 20px;
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
}

.gift-details {
  h5 {
    font-size: rem(24);
    font-weight: 800;
    color: $dark-charcoal;
    margin-bottom: 8px;
    line-height: 32px;
    font-family: var(--font-bricolage); font-optical-sizing: none;
    display: flex;

    .gift-text{
      width: 118px;
      @include rtl-styles {
        width: 134px;
      }
    }
  }
  p {
    font-size: rem(14);
    color: $grey-text;
    font-weight: 500;
    line-height: 18px;
    letter-spacing: -0.14px;

    >span{
      color: $dark-charcoal;
    }
  }

  &__happy-card {
    margin-top: 24px;
    display: flex;
    gap: 8px;
    border-radius: 6px;

    &-image {
      width: 104px;
      height: 66px;

      img {
        display: block;
        width: 100%;
        height: 100%;
        border-radius: 6px;
      }
    }

    &-title {
      display: flex;
      justify-content: center;
      flex-direction: column;
      gap: 6px;
      p {
        color: $dark-charcoal;
        font-size: rem(14);
        font-weight: 600;
        line-height: 16px;
        letter-spacing: -0.14px;
      }
      &-text {
        color: $dark-charcoal;
        font-size: rem(16);
        font-weight: 700;
        line-height: 24px;
        letter-spacing: -0.16px;
      }
    }
  }
}

.btn-wrapper {
  margin-top: 31px;
}

.marquee {
  direction: ltr !important;
  scrollbar-width: none;
  width: 230px !important;

  @include rtl-styles{
    width: 210px !important;
  }

  &::-webkit-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
    display: none;
    width: 0px;
  }
}