'use client';
import styles from './Personalize.module.scss';
import PersonalizeLayout from '@/features/gifts/personalize/personalizeLayout/PersonalizeLayout';
import CameraTag from '@/features/common/cameraTag/CameraTag';
import { imageBaseUrl } from '@/constants/envVariables';
import { useTranslation } from 'react-i18next';
import Notifier from '@/features/common/notifier/Notifier';
import { useParams, useRouter } from 'next/navigation';
import { PAGEURLS } from '@/features/gifts/constants/gifts.constants';
import { useEffect, useState } from 'react';
import { HAS_WINDOWS_REF } from '@/utils/hasWindowRef';
import ContributionHeader from '@/features/common/header/contributionHeader/ContributionHeader';
import GoogleTagManager from '@/utils/googleTagManager';
import {
  CLEVERTAP_EVENTS,
  CLEVERTAP_PROPERTIES,
  GTM_EVENTS,
  PLATFORM_TYPE,
} from '@/constants/common';
import getRegionAndLocale from '@/utils/getRegionAndLocale';
import { pushCleverTapEvent } from '@/features/common/clevertap/clevertap.services';
import Button from '@/features/common/button/Button';
import useContributionSlice from '@/features/gifts/contributionFlowSlice';
import { useAppSelector } from '@/redux/hooks';

const Page = () => {
  const { t } = useTranslation();
  const backButton = `${imageBaseUrl}/images/icons/arrow-back.svg`;
  const router = useRouter();
  const params = useParams();
  const { region } = getRegionAndLocale();
  const [openEditor, setOpenEditor] = useState(false);
  const [isLoading, setIsLoading] =  useState<boolean>(false)

  const { getPersonalisation } = useContributionSlice();

  const personalisationData: any = useAppSelector(getPersonalisation);
  const hideEditBtn: boolean =
    personalisationData?.photo?.file ||
    personalisationData?.video?.videoInfo ||
    personalisationData?.gif?.url;

  const goBack = () => {
    router.replace(
      `${PAGEURLS.GROUP_GIFTS}/${params?.giftid}${PAGEURLS.GREETING}`
    );
  };

  /**
   * @method onClickedEdit
   */
  const onClickedEdit = () => {
    setOpenEditor(true);
  };

  const handleRedirection = () => {
    setIsLoading(true)
    // #. GTM Event
    trackPersonalizeEvent('Skip');
    router.replace(`${PAGEURLS.GROUP_GIFTS}/${params?.giftid}${PAGEURLS.MESSAGE}`);
  };

    /**
   * @method trackPersonalizeEvent
   */
    const trackPersonalizeEvent = (media: 'Skip') => {
      // #. Push GA4 event
  
      const { push: gtmPush } = GoogleTagManager();
  
      const gtmData = {
        media: media,
        gg_id: params?.giftid,
      };
  
      gtmPush(GTM_EVENTS.PERSONALIZE, {
        store: region?.toUpperCase(),
        items: [{ ...gtmData }],
      });
  
      // #. Push CleverTap event
      const clevertapData = {
        [CLEVERTAP_PROPERTIES.PLATFORM]: PLATFORM_TYPE.WEB,
        [CLEVERTAP_PROPERTIES.STORE]: region?.toUpperCase(),
        [CLEVERTAP_PROPERTIES.MEDIA]: media,
        [CLEVERTAP_PROPERTIES.GG_ID]: params?.giftid,
      };
  
      pushCleverTapEvent(CLEVERTAP_EVENTS.GG_PERSONALIZE, clevertapData);
    };

  useEffect(() => {
    HAS_WINDOWS_REF && window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  return (
    <>
      <ContributionHeader />
      <div className={styles['personalize']}>
        <div
          className={`${styles['personalize__top-section']} amount-selection`}
        >
          <div className={styles['personalize__progress']}>
            <div className={styles['personalize__blk']}>
              <span onClick={goBack}>
                <img src={backButton} alt="img" />
              </span>

              <h4 className={styles['personalize__title']}>
                {t('personalize')}
              </h4>
            </div>
            <div>
              {!hideEditBtn ? (
                <Button
                  className={
                    isLoading ? styles['personalize__skip-loader'] : ''
                  }
                  loader={isLoading}
                  action={handleRedirection}
                  theme="small-grey"
                >
                  <span>{t('skip')}</span>
                </Button>
              ) : (
                <Button action={onClickedEdit} theme="small-grey">
                  <span>{t('edit')}</span>
                </Button>
              )}
            </div>
          </div>
        </div>
        <CameraTag />
        <Notifier />
        <PersonalizeLayout
          openEditor={openEditor}
          setOpenEditor={setOpenEditor}
          onClickedEdit={onClickedEdit}
        />
      </div>
    </>
  );
};

export default Page;
