@import '@/styles/abstracts/abstracts';

.personalize {
  &__blk{
    display: flex;
    align-items: center;
    gap: 8px;
  }
  &__progress {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    justify-content: space-between;

    span {
      height: 32px;
      display: flex;
      align-items: center;
      cursor: pointer;
    }
    h4 {
      font-family: var(--font-bricolage); font-optical-sizing: none;
      font-size: 32px;
      font-weight: 800;
      line-height: 32px;
      letter-spacing: -0.16px;
      color: $dark-charcoal;
    }
    img {
      display: block;
      @include rtl-rotate;
    }
  }

  &__skip-loader{
    span span {
      width: 25px !important;
      height: 25px !important;

      svg{
        color: $black;
      }
    }
  }

  &__image-container {
    margin-top: 24px;
    width: 100%;
    height: 98px;
  }
}
