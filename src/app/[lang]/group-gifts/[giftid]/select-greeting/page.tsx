'use client';

import React, { useEffect, useRef, useState } from 'react';
import styles from './SelectGreeting.module.scss';
import { imageBaseUrl } from '@/constants/envVariables';
import { Skeleton } from '@mui/material';
import Button from '@/features/common/button/Button';
import BrandSliderItem from '@/features/gifts/selectGreeting/sliderItem/SliderItem';
import { useParams, useRouter } from 'next/navigation';
import { CONTRIBUTION_CONGIF, PAGEURLS } from '@/features/gifts/constants/gifts.constants';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import useContributionSlice from '@/features/gifts/contributionFlowSlice';
import useGiftsAPI from '@/features/gifts/giftsAPI';
import GreetingsSkelton from '@/features/gifts/contentLoader/grettingsSkeleton/GreetingsSkelton';
import { useTranslation } from 'react-i18next';
import { HAS_WINDOWS_REF } from '@/utils/hasWindowRef';
import getRegionAndLocale from '@/utils/getRegionAndLocale';
import ContributionHeader from '@/features/common/header/contributionHeader/ContributionHeader';
import { BUTTON_TYPE } from '@/constants/common';

interface occasionInterface {
  name: string;
  nameAr: string;
  nameEn: string;
  code: string;
  greetinghubImageUrl: string;
  isAnimated: boolean; 
  language: string;
}

const SelectGreeting = () => {
  const router = useRouter();
  const params = useParams();
  const { t } = useTranslation();
  const { locale, region } = getRegionAndLocale();

  // #. For dispatch values
  const { getOccasion, setOccasion, getContributionConfig, setContributionConfig } = useContributionSlice();
  const occasionData: occasionInterface = useAppSelector(getOccasion);
  const { default_greetings: defaultGreetings } :any = useAppSelector(getContributionConfig);
  const dispatch = useAppDispatch();
  const [isAnimated, setIsAnimated] = useState(false);
  const [language, setLanguage] = useState('en');
  const [active, setActive] = useState('');
  const [occasionName, setOccasionName] = useState('');
  const [isLoading, setIsLoading] =  useState<string>("")

  const backButton = `${imageBaseUrl}/images/icons/arrow-back.svg`;

  //#.Fetching API data
  const { FetchBrandIllustrations } = useGiftsAPI();
  const { illustrationData, illustrationLoading } = FetchBrandIllustrations(
    occasionData?.code,
    language,
    isAnimated,
    language,
    defaultGreetings
  );

  // handle next/prev
  // @ts-ignore
  const swiperRef = useRef<swiper | null>(null);
  const nextImage = `${imageBaseUrl}/images/icons/arrow-with-circle-bg.svg`;
  const handleNext = () => {
    if (swiperRef.current && swiperRef.current?.swiper) {
      swiperRef.current.swiper.slideNext();
    }
  };
  const handlePrev = () => {
    if (swiperRef.current && swiperRef.current!.swiper) {
      swiperRef.current!.swiper.slidePrev();
    }
  };

  /**
   * The handleRedirection
   */
  const handleRedirection = () => {
    setIsLoading(BUTTON_TYPE.CONTINUE)
    router.replace(
      `${PAGEURLS.GROUP_GIFTS}/${params?.giftid}${PAGEURLS.PERSONALIZE}`
    );
    setOccasion({
      name: occasionData?.name,
      nameAr: occasionData?.nameAr,
      nameEn: occasionData?.nameEn,
      code: occasionData?.code,
      greetinghubImageUrl: active,
      isAnimated: isAnimated,
    });
  };

  /**
   * The handleSkipped
   */
  const handleSkipped = () => {
    setIsLoading(BUTTON_TYPE.SKIP)
    router.replace(
      `${PAGEURLS.GROUP_GIFTS}/${params?.giftid}${PAGEURLS.PERSONALIZE}`
    );

    dispatch(
      setOccasion({
        name: occasionData?.name,
        nameAr: occasionData?.nameAr,
        nameEn: occasionData?.nameEn,
        code: occasionData?.code,
        greetinghubImageUrl: '',
        isAnimated: false,
        language: 'en',
      })
    );
  };

  /**
   * Handle language change
   */
  const handleGif = () => {
    setIsAnimated((prevIsAnimated) => !prevIsAnimated);
    dispatch(setContributionConfig({}));
    dispatch(
      setOccasion({
        name: occasionData?.name,
        nameAr: occasionData?.nameAr,
        nameEn: occasionData?.nameEn,
        code: occasionData?.code,
        greetinghubImageUrl: '',
        language: locale,
      })
    );
  };

  /**
   * Handle language change
   */
  const handleLanguage = () => {
    setLanguage((prevLanguage) => (prevLanguage === 'en' ? 'ar' : 'en'));
    dispatch(
      setOccasion({
        name: occasionData?.name,
        nameAr: occasionData?.nameAr,
        nameEn: occasionData?.nameEn,
        code: occasionData?.code,
        greetinghubImageUrl: '',
        language: locale,
      })
    );
  };

  // #. Greetings data
  const greetingsData = isAnimated
    ? illustrationData?.gifIllustrations?.edges
    : illustrationData?.illustrations?.edges;

  /**
   * @method onActive
   * @description Set selected image
   * @param data
   * @param isAnimated
   */
  const onActive = (data: any, isAnimated: boolean) => {
    const activeItem = isAnimated ? data?.node?.gifFile : data?.node?.cardImage;
    setActive(activeItem);
    dispatch(
      setOccasion({
        name: occasionData?.name,
        nameAr: occasionData?.nameAr,
        nameEn: occasionData?.nameEn,
        code: occasionData?.code,
        greetinghubImageUrl: activeItem,
        greetingsHubReference: data?.node?.referenceCode,
        isAnimated: isAnimated,
        staticGifPath: isAnimated ? data?.node?.gifImage : '',
        language: language,
      })
    );
  };

  const goBack = () => {
    router.replace(
      `${PAGEURLS.GROUP_GIFTS}/${params?.giftid}${PAGEURLS.CONTRIBUTION}`
    );
  };

  useEffect(() => {
    setOccasionName(
      occasionData?.name && locale === 'ar'
        ? occasionData?.nameAr
        : occasionData?.nameEn
    );
    setIsAnimated(occasionData?.isAnimated ? occasionData?.isAnimated : defaultGreetings === CONTRIBUTION_CONGIF.ANIMATED ? true : false);
    setLanguage(occasionData?.language ? occasionData?.language : 'en');
    setActive(occasionData?.greetinghubImageUrl);
  }, []);

  useEffect(() => {
    HAS_WINDOWS_REF && window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  return (
    <>
    <ContributionHeader/>
    <div className={styles['greetings']}>
      <div className={`${styles['greetings__top-section']} amount-selection`}>
        {occasionName?.length === 0 ? (
          <Skeleton
            variant="text"
            animation="wave"
            height={43}
            width={130}
            className={styles['skeleton']}
          />
        ) : (
          <div className={styles['greetings__progress']}>
            <div className={styles['greetings__blk']}>
            <span onClick={goBack}>
              <img src={backButton} alt="img" />
            </span>

            <h4 className={styles['greetings__title']}>{occasionName}</h4>
          </div>
          <div>
                <Button
                  action={handleSkipped}
                  loader={isLoading == BUTTON_TYPE.SKIP}
                  theme="small-grey"
                  className={`${styles['greetings__skip']} ${
                    isLoading == BUTTON_TYPE.SKIP &&
                    styles['greetings__skip-loader']
                  }`}
                >
                  <span>{`${t('skip')}`}</span>
                </Button>
            </div>
          </div>
        )}

      </div>
      <div className={styles['greetings__top-buttons']}>
        <div
          className={`${styles['greetings__top-buttons-button']} ${
            isAnimated || (defaultGreetings === CONTRIBUTION_CONGIF.ANIMATED) ? styles['greetings__top-buttons--enabled'] : ''
          }`}
          onClick={handleGif}
        >
          {t('animated')}
        </div>
        <div
          className={`${styles['greetings__top-buttons-button']} ${
            language === 'ar' ? styles['greetings__top-buttons--enabled'] : ''
          }`}
          onClick={handleLanguage}
        >
          {t('arabic')}
        </div>
      </div>
      {illustrationLoading ? (
        <GreetingsSkelton />
      ) : (
        <div
          className={`${styles['greetings__greeting-swiper']} ${greetingsData?.length === 0 ? styles['greetings__greeting-empty']:''} brand-greetings`}
        >
          {greetingsData?.length !== 0 && (
            <>
             { greetingsData?.map((item) => (
                  <BrandSliderItem
                    data={item}
                    isAnimated={isAnimated}
                    onActive={onActive}
                    selected={active}
                  />
              ))}
            </>
          )}

          {greetingsData?.length === 0 && (
            <p className={styles['empty-container']}>{t('noGretingCover')}</p>
          )}
        </div>
      )}

      <div className={`${styles['greetings__btn-wrapper']}`}>
       {active && <img src={active} width={35} height={50}/>}
        <Button
          theme="primary"
          className={styles['greetings__btn-continue']}
          action={handleRedirection}
          attribues={{
            disabled: !active?.length || isLoading,
          }}
          loader={isLoading == BUTTON_TYPE.CONTINUE}
        >
          {t('continue')}
        </Button>
      </div>
    </div>
    </>
  );
};

export default SelectGreeting;
