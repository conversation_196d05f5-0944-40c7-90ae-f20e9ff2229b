@import '@/styles/abstracts/abstracts';

.greetings {

  &__blk{
    display: flex;
    align-items: center;
    gap: 8px;
  }
  &__progress {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24px;
    gap: 8px;

    span {
      height: 32px;
      display: flex;
      align-items: center;
      cursor: pointer;
    }
    h4 {
      font-family: var(--font-bricolage); font-optical-sizing: none;
      font-size: 32px;
      font-weight: 800;
      line-height: 32px;
      letter-spacing: -0.16px;
      color: $dark-charcoal;
    }
    img {
      display: block;
      @include rtl-rotate;
    }
  }

  &__top-buttons {
    margin-top: 20px;
    display: flex;
    gap: 8px;

    &-button {
      cursor: pointer;
      background-color: $medium-light-grey;
      font-size: rem(14);
      padding: 4px 12px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 500;
      line-height: 18px;
      letter-spacing: -0.14px;
      color: $dark-charcoal;
    }

    &--enabled {
      background-color: $dark-charcoal;
      color: #fff;
    }
  }

  &__greeting-swiper {
    margin-top: 24px;
    display: flex;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 16px;
    max-height: 550px;
    overflow-y: scroll;
    padding-bottom: 10px;
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  &__greeting-empty{
    min-height: 500px;
    justify-content: center;
    align-items: center;
  }

  &__btn-wrapper {
    position: absolute;
    bottom: 0;
    width: 100%;
    left: 0;
    padding-inline: 20px;
    padding-block: 16px;
    box-shadow: 0px -4px 20px 0px rgba(0, 0, 0, 0.06);
    background-color: $white;
    display: flex;
    align-items: center;
    gap: 8px;

    >img{
      border-radius: 4px;
      border: 1px solid $white;
    }
  }
  
  &__btn-continue {
      height: 50px;
      font-size: rem(16);
      font-weight: 700;
      line-height: 24px;
      letter-spacing: -0.16px;
  }

  &__skip-loader{
    span{
      width: 25px !important;
      height: 25px !important;

      svg{
        color: $black;
      }
    }
  }
}

.skeleton {
  margin-bottom: 10px !important;
  // margin-left: 30px;
}

.empty-container {
  font-size: 16px;
  font-weight: 700;
  line-height: 24px;
  letter-spacing: -0.16px;
  color: $mild-red;
}
