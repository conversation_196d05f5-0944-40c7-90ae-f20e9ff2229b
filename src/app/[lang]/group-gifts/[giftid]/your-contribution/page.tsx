'use client';
import {
  CONTRIBUTION,
  GTM_VALUES,
  IS_GUEST_USER_COOKIE,
} from '@/constants/common';
import useCommonSlice from '@/features/common/commonSlice';
import useContributionSlice from '@/features/gifts/contributionFlowSlice';
import useGiftsAPI from '@/features/gifts/giftsAPI';
import YourContribution from '@/features/gifts/yourContribution/YourContribution';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import { getCookie } from '@/utils/getAndsetCookie';
import getRegionAndLocale from '@/utils/getRegionAndLocale';
import { HAS_WINDOWS_REF } from '@/utils/hasWindowRef';
import { useParams, useRouter } from 'next/navigation';
import { useEffect } from 'react';

const Page = () => {
  const params = useParams();
  const router = useRouter();
  const referenceId = params?.giftid;
  const { locale, region }: any = getRegionAndLocale();
  const { getTokenInfo, getGuestInfo } = useCommonSlice();
  const { AccessToken }: any = useAppSelector(getTokenInfo);
  const { GuestAccessToken }: any = useAppSelector(getGuestInfo);
  const isGuestLoginCookie: any = getCookie(IS_GUEST_USER_COOKIE);
  const { FetchYourContributionDetail, FetchGroupGiftDetail } = useGiftsAPI();

  const dispatch = useAppDispatch();

  // #. For dispatch values
  const { setContributorType } = useContributionSlice();

  const { contributionData, contributionError, contributionLoading } =
    FetchYourContributionDetail(
      referenceId,
      locale,
      isGuestLoginCookie === 'true' ? GuestAccessToken : AccessToken,
      isGuestLoginCookie
    );

  useEffect(() => {
    dispatch(
      setContributorType(
        contributionData?.groupGiftDetail?.isOrganizer
          ? GTM_VALUES.ORGANIZER
          : GTM_VALUES.CONTRIBUTER
      )
    );
  }, [contributionData]);

  const giftType =
    contributionData?.groupGiftDetail?.contributionConfiguration?.type;

  useEffect(() => {
    HAS_WINDOWS_REF && window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  return (
    <>
      {contributionData && (
        <YourContribution
          type={giftType || ''}
          groupGiftDetail={contributionData}
        />
      )}
    </>
  );
};

export default Page;
