'use client';
import React, { useEffect, useState } from 'react';
import styles from './PaymentSuccess.module.scss';
import Button from '@/features/common/button/Button';
import useCommonSlice from '@/features/common/commonSlice';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import useGiftsAPI from '@/features/gifts/giftsAPI';
import { useTranslation } from 'react-i18next';
import { useParams, useSearchParams } from 'next/navigation';
import { useQuery } from '@apollo/client';
import { useRouter } from 'next/navigation';
import { DOWNLOAD_APP_QUERY } from '@/features/common/common.query';
import PaymentSuccessSkelton from '@/features/gifts/contentLoader/paymentSuccessSkelton/PaymentSuccessSkelton';
import { getCookie } from '@/utils/getAndsetCookie';
import {
  CLEVERTAP_EVENTS,
  CLEVERTAP_PROPERTIES,
  EGG_REWARDS_SITE_DOMIAN,
  GTM_EVENTS,
  GTM_VALUES,
  IS_GUEST_USER_COOKIE,
  PAGEURLS,
  PLATFORM_TYPE,
} from '@/constants/common';
import Marquee from 'react-fast-marquee';
import getRegionAndLocale from '@/utils/getRegionAndLocale';
import GoogleTagManager from '@/utils/googleTagManager';
import { PURCHASE_TYPE } from '@/features/startGroupGift/constants/startGroupGift.constants';
import { pushCleverTapEvent } from '@/features/common/clevertap/clevertap.services';
import useContributionSlice from '@/features/gifts/contributionFlowSlice';
import useGuestUserSlice from '@/features/gifts/giftsOpen/guestUserSlice';
import { HAS_WINDOWS_REF } from '@/utils/hasWindowRef';

const PaymentSuccess = () => {
  // translations
  const { t } = useTranslation();
  const searchParams = useSearchParams();
  const transactionId = searchParams.get('transaction_id');
  const isRewards = searchParams.get('embedded');
  const [loading, setLoading] = useState<boolean>();
  const params = useParams();
  const referenceId = params?.giftid;
  const router = useRouter();
  const { locale, region } = getRegionAndLocale();
  const dispatch = useAppDispatch();

  const { getTokenInfo, getGuestInfo } = useCommonSlice();
  const { AccessToken }: any = useAppSelector(getTokenInfo);
  const { GuestAccessToken }: any = useAppSelector(getGuestInfo);
  const isGuestLoginCookie: any = getCookie(IS_GUEST_USER_COOKIE);
  const { HandlePaymentCheckoutVerification } = useGiftsAPI();
  const isInIframe = HAS_WINDOWS_REF && (window.location !== window.parent.location);
  const rewardsSiteDomain = getCookie(EGG_REWARDS_SITE_DOMIAN);
  // Remove surrounding double quotes if they exist
  const sanitizedRewardsSiteDomain = rewardsSiteDomain.replace(/^"|"$/g, '');
  const rewardsAppHomeUrl = `${sanitizedRewardsSiteDomain}/corporate/gift-cards/groupgift`;
  const rewardsAppHomeUrlAr = `${sanitizedRewardsSiteDomain}/ar/corporate/gift-cards/groupgift/`;
  
  // #. Clear Redux persisted values
  const { clearState } = useContributionSlice();
  const { clearGuestSlice } = useGuestUserSlice();

  //Download app data getting from webstore API
  const { data } = useQuery<any>(DOWNLOAD_APP_QUERY, {
    context: {
      clientName: 'webstore-with-cdn',
    },
  });

  // #.Set app store images
  const appBanner = data?.downloadApp[0]?.roundedAppBanner;

  // #.Set payload for payment verify API
  const input = {
    transactionId,
  };

  const { paymentVerificationData, paymentVerificationLoading } =
    HandlePaymentCheckoutVerification(
      input,
      isGuestLoginCookie === 'true' ? GuestAccessToken : AccessToken,
      referenceId,
      locale,
      isGuestLoginCookie
    );

  // #. Set payment and gift detail data.
  const paymentData = paymentVerificationData?.checkoutVerification;
  const giftDetailData = paymentVerificationData?.groupGiftDetail;
  const successMessage = paymentData?.successMessage[0]?.message[0];
  const qitafMessage = paymentData?.successMessage[1]?.message[0];
  const rewardsSuccessImage = '/images/celebrate-rewards-image.png';
  const eggSuccessImage = '/images/celebrate-image.png';

  useEffect(() => {
    if (giftDetailData) {
      // #. Push GA4 event
      const { push: gtmPush } = GoogleTagManager();

      const gtmData = {
        purchase_type:
          giftDetailData?.purchaseMode === PURCHASE_TYPE.MY_SELF
            ? GTM_VALUES.BUY_FOR_SELF
            : GTM_VALUES.SEND_AS_GIFT,
        occasion: giftDetailData?.occasion?.code,
        item_name: giftDetailData?.brand?.name,
        item_id: giftDetailData?.brand?.code,
        contributor_type: giftDetailData?.isOrganizer
          ? GTM_VALUES.ORGANIZER
          : GTM_VALUES.CONTRIBUTER,
        value:
          giftDetailData?.currency?.isoCode +
          ' ' +
          giftDetailData?.contributionConfiguration?.amount,
        receiver_name: giftDetailData?.receiverInfo?.name,
        gg_id: params?.giftid,
      };

      gtmPush(GTM_EVENTS.PURCHASE, {
        store: region?.toUpperCase(),
        items: [{ ...gtmData }],
      });

      // #. Push CleverTap event

      const reveiverPhone = giftDetailData?.receiverInfo?.phone?.number;
      const reveiverCountryCode =
        giftDetailData?.receiverInfo?.phone?.countryCode;

      const clevertapData = {
        [CLEVERTAP_PROPERTIES.PLATFORM]: PLATFORM_TYPE.WEB,
        [CLEVERTAP_PROPERTIES.STORE]: region?.toUpperCase(),
        [CLEVERTAP_PROPERTIES.PURCHASE_TYPE]:
          giftDetailData?.purchaseMode === PURCHASE_TYPE.MY_SELF
            ? GTM_VALUES.BUY_FOR_SELF
            : GTM_VALUES.SEND_AS_GIFT,
        [CLEVERTAP_PROPERTIES.OCCASION]: giftDetailData?.occasion?.code,
        [CLEVERTAP_PROPERTIES.BRAND_ID]: giftDetailData?.brand?.code,
        [CLEVERTAP_PROPERTIES.BRAND]: giftDetailData?.brand?.name,
        [CLEVERTAP_PROPERTIES.CONTRIBUTOR_TYPE]: giftDetailData?.isOrganizer
          ? GTM_VALUES.ORGANIZER
          : GTM_VALUES.CONTRIBUTER,
        [CLEVERTAP_PROPERTIES.AMOUNT]:
          giftDetailData?.currency?.isoCode +
          ' ' +
          giftDetailData?.contributionConfiguration?.amount,
        [CLEVERTAP_PROPERTIES.RECEIVER_NAME]:
          giftDetailData?.receiverInfo?.name,
        [CLEVERTAP_PROPERTIES.RECEIVER_EMAIL]:
          giftDetailData?.receiverInfo?.email,
        [CLEVERTAP_PROPERTIES.RECEIVER_CONTACT]: reveiverPhone
          ? reveiverCountryCode + reveiverPhone
          : '',
        [CLEVERTAP_PROPERTIES.GG_ID]: params?.giftid,
        [CLEVERTAP_PROPERTIES.CONTRIBUTOR_EMAIL]: paymentData?.email,
      };
      pushCleverTapEvent(CLEVERTAP_EVENTS.GG_CHARGED, clevertapData);
    }
  }, [paymentVerificationData]);

  /**
   * @method handleRedirect
   * @description Redirect to the landing page based on conditions
   */
  const handleRedirect = () => {
    setLoading(true);
    try {
      const redirectUrl =
        locale === 'ar' ? rewardsAppHomeUrlAr : rewardsAppHomeUrl;
      if (redirectUrl && isInIframe && window.top) {
        window.top.location.href = redirectUrl;
      } else {
        router.push('/', undefined);
      }
    } catch (error) {
      console.error('Redirect Error:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    dispatch(clearState());
    dispatch(clearGuestSlice()); // cleared redux stored values
  }, []);

  return (
    <>
      {!paymentVerificationLoading ? (
        <div className={styles['success']}>
          <div className={styles['success__top-container']}>
            {!isRewards ? (
              giftDetailData?.title?.length > 20 ? (
                <Marquee
                  speed={30}
                  className={styles['marquee']}
                  direction={locale === 'ar' ? 'right' : 'left'}
                >
                  <p className={styles['success__top-container-title']}>
                    {giftDetailData?.title}
                  </p>
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                </Marquee>
              ) : (
                <p className={styles['success__title']}>
                  {giftDetailData?.title}
                </p>
              )
            ) : (
              ''
            )}

            {!!isRewards ? (
              <img src="/images/ygag-logo.png" width={59} height={40} />
            ) : giftDetailData?.organizerName?.length > 25 ? (
              <div className={styles['success__marquee']}>
                <span className={styles['success__marquee-text']}>
                  {t('organizedByText')}
                </span>
                <Marquee
                  speed={30}
                  className={styles['marquee']}
                  direction={locale === 'ar' ? 'right' : 'left'}
                >
                  <span>{giftDetailData?.organizerName}</span>
                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                </Marquee>
              </div>
            ) : (
              <p className={styles['success__organizer-name']}>
                {t('organizedByText')}&nbsp;
                <span>{giftDetailData?.organizerName}</span>
              </p>
            )}
            {!isRewards && (
              <img
                className={styles['check-circle']}
                src="/images/icons/check-circle.png"
                alt="check-circle"
              />
            )}
            <div
              className={` ${styles['success__message']}
            ${!!isRewards ? styles['success__message-rew'] : ''}
            `}
            >
              <p>
                <span> {paymentData?.currency} </span>
                <span> {paymentData?.amount} </span>
              </p>
              <p>{t('successfullyPaid')}</p>
            </div>
            {!!isRewards && (
              <div className={styles['success__message-rewards']}>
                <span>{t('for')}</span>&nbsp;
                <span>{giftDetailData?.title}</span>
              </div>
            )}
          </div>

          {qitafMessage && (
            <div className={styles['success__qitaf-message']}>
              <p>{qitafMessage}</p>
            </div>
          )}

          <div className={styles['success__login-message']}>
              <p>{successMessage}</p>
            </div>
          <div
            className={`${styles['success__bottom-container']}`}
          >
            <p>{t('downloadApp')}</p>
            <div className={styles['success__downloadApp']}>
              {appBanner?.map((item: any, index: number) => (
                <a 
                  href={!!isRewards ? undefined : item?.itemUrl}
                  onClick={(e) => {
                    if (!!isRewards) {
                      e.preventDefault(); // Prevent the default href behavior in iframe
                      if (window.top) {
                        window.top.location.href = item?.itemUrl; // Redirect using window.top
                      }
                    }
                    // No else needed, as href will handle the redirection when not in iframe
                  }}
                  key={index}>
                  <img src={item?.itemImage} alt={item?.itemName} />
                </a>
              ))}
            </div>
            <div className={styles['btn-container']}>
            <Button
              theme="primary"
              action={handleRedirect}
              className={styles['back-to-Btn']}
              loader={loading}
            >
              {t('done')}
            </Button>
            </div>
          </div>

         {!!isRewards && <img
            className={` ${styles['success__celebrate-img']}
            ${styles['success__celebrate-img-rewards']}
            `}
            src={rewardsSuccessImage}
            alt="celebrate-img"
          />}
        </div>
      ) : (
        <PaymentSuccessSkelton />
      )}
    </>
  );
};

export default PaymentSuccess;
