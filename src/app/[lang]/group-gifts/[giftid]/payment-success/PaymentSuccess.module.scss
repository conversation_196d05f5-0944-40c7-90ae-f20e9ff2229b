@import '@/styles/abstracts/abstracts';

.success {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;

  &__marquee {
    display: flex;
    align-items: center;
    gap: 5px;
    color: $dark-charcoal;
    font-weight: 600;
    font-size: rem(14);
    margin-top: rem(8);
    line-height: 18px;
    letter-spacing: -0.14px;

    &-text {
      width: 133px;
      font-size: rem(14);
      font-weight: 500;

      @include rtl-styles {
        width: 158px;
      }
    }
  }

  &__title {
    font-size: rem(32);
    color: $dark-charcoal;
    font-family: var(--font-bricolage); font-optical-sizing: none;
    font-weight: 800;
    line-height: 40px;
    letter-spacing: -0.16px;
  }

  &__organizer-name {
    font-size: rem(14);
    color: $dark-charcoal;
    margin-top: rem(8);
    font-weight: 500;
    font-size: rem(14);
    margin-top: rem(8);
    line-height: 18px;
    letter-spacing: -0.14px;

    span {
      font-weight: 600;
    }
  }

  &__top-container {
    text-align: center;
    border-radius: 0% 0% 50% 50% / 0% 0% 14% 14%;
    z-index: 2;

    &-title {
      font-size: rem(32) !important;
      color: $dark-charcoal;
      font-family: var(--font-bricolage); font-optical-sizing: none;
      font-weight: 800;
      line-height: 40px;
      letter-spacing: -0.16px;
    }

    .check-circle {
      width: 100%;
      position: absolute;
      left: 0;
      top: 128px;
    }
  }

  &__message {
    margin-top: rem(154);
    &-rew{
      margin-top: 195px;
    }

    &-rewards {
      display: flex;
      align-items: center;
      justify-content: center;
      color: #545454;
      font-size: 16px;
      margin-top: rem(24);
      font-weight: 500;

      span:last-child {
        color: $dark-charcoal;
        font-weight: 600;
      }
    }
    p:first-child {
      color: $dark-charcoal;
      font-size: rem(32);
      font-weight: 800;
      line-height: 40px;
      letter-spacing: -0.16px;
      font-family: var(--font-bricolage); font-optical-sizing: none;
    }

    p:last-child {
      color: $dark-charcoal;
      font-size: rem(32);
      font-weight: 800;
      line-height: 40px;
      letter-spacing: -0.16px;
      font-family: var(--font-bricolage); font-optical-sizing: none;
      margin-top: rem(4);
    }
  }

  &__qitaf-message {
    margin-top: rem(24);
    > p {
      font-size: rem(14);
      text-align: center;
      color: $dark-charcoal;
      font-weight: 500;
      line-height: 18px;
      letter-spacing: -0.14px;
    }
  }

  &__bottom-container {
    z-index: 2;
    position: absolute;
    bottom: 0;
    left: 0;
    padding-inline: 20px;
    width: 100%;

    > p {
      text-align: center;
      color: $dark-charcoal;
      font-size: rem(14);
      font-weight: 600;
      line-height: 24px;
      letter-spacing: -0.14px;
      margin-bottom: 16px;
    }

    .btn-container{
      position: absolute;
      bottom: 0;
      width: 100%;
      left: 0;
      padding: 16px 20px;
      box-shadow: 0px -4px 20px 0px rgba(0, 0, 0, 0.06);
      border-top-left-radius: 20px;
      border-top-right-radius: 20px;
    }
  }

  &__login-message {
    margin-top: 64px;
    padding: rem(16);
    background-color: rgba(210, 201, 255, 0.25);
    border-radius: 12px;
    color: $dark-charcoal;

    p {
      font-size: 14px;
      font-weight: 500;
      line-height: 18px;
      letter-spacing: -0.14px;
    }
  }

  &__downloadApp {
    display: flex;
    justify-content: center;
    margin-bottom: rem(114);
    flex-wrap: wrap;
    gap: 16px;

    img {
      height: rem(48);
      width: rem(48);
      cursor: pointer;
      border-radius: 50%;
    }
  }

  .back-to-Btn {
    padding: rem(13);
    width: 100%;
    font-size: rem(16);
    font-weight: 600;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__celebrate-img {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    &-rewards {
      top: 56px;
    }
  }
}

.marquee {
  direction: ltr !important;
  scrollbar-width: none;

  &::-webkit-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
    display: none;
    width: 0px;
  }
}
