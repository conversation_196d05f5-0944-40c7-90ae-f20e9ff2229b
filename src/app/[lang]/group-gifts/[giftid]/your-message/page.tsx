'use client';
import { useEffect, useState } from 'react';
import styles from '@/features/gifts/yourMessage/YourMessage.module.scss';
import Button from '@/features/common/button/Button';
import BackGroundColorMenu from '@/features/gifts/yourMessage/BackGroundColorMenu';
import FontFamilyMenu from '@/features/gifts/yourMessage/FontFamilyMenu';
import FontSizeMenu from '@/features/gifts/yourMessage/FontSizeMenu';
import { useParams, useRouter } from 'next/navigation';
import {
  FONT_FAMILIES,
  PAGEURLS,
} from '@/features/gifts/constants/gifts.constants';
import { imageBaseUrl } from '@/constants/envVariables';
import { useTranslation } from 'react-i18next';
import getRegionAndLocale from '@/utils/getRegionAndLocale';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import useContributionSlice from '@/features/gifts/contributionFlowSlice';
import useCommonSlice from '@/features/common/commonSlice';
import Link from 'next/link';
import { HAS_WINDOWS_REF } from '@/utils/hasWindowRef';
import ContributionHeader from '@/features/common/header/contributionHeader/ContributionHeader';
import CustomLink from '@/features/common/customLink/CustomLink';
import { BUTTON_TYPE } from '@/constants/common';

/**
 * @method BrandUserMessage
 * @description Brand user custom message component
 * @returns
 */
const YourMessage = (): JSX.Element => {
  const { locale, region } = getRegionAndLocale();
  const { t } = useTranslation();
  const router = useRouter();
  const params = useParams();
  // taking public image config url
  const backButton = `${imageBaseUrl}/images/icons/arrow-back.svg`;

  const placeHolderText = t('usermessage');
  const charatersText = t('characters');
  const MAX_MESSSAGE_LENGTH = 720;

  // #. Notification dispatch
  const { setNotifierState } = useCommonSlice();
  const notifierDispatch = useAppDispatch();

  // #. Notifier state
  const [disableContinue, setDisableContinue] = useState<boolean>(false);
  const [messages, setMessages] = useState<string>('');
  const [color, setColor] = useState<string>('#EB6B6B');
  const [fontSize, setFontSize] = useState<string>('18');
  const [fontFamily, setFontFamily] = useState<string>('');
  const [isLoading, setIsLoading] = useState<string>("")

  // #. Notification dispatch
  const { setMessage, getMessage } = useContributionSlice();
  const dispatch = useAppDispatch();
  const getMessages: any = useAppSelector(getMessage);

  /**
   * @method onMessageUpdated
   */
  const onMessageUpdated = (event: any) => {
    const message = event?.target?.value?.replace(/(<|>)/g, '');
    setMessages(message);
    dispatch(
      setMessage({
        message: message,
        backgroundColor: color,
        fontFamily: fontFamily,
        fontSize: fontSize,
        isDirty: false,
      })
    );
  };

  /**
   * @method onBackGroundColorUpdated
   */
  const onBackGroundColorUpdated = (colors: string) => {
    dispatch(
      setMessage({
        message: messages,
        backgroundColor: colors,
        fontFamily: fontFamily,
        fontSize: fontSize,
        isDirty: false,
      })
    );
  };

  /**
   * @method onFontSizeUpdated
   */
  const onFontSizeUpdated = (size: number) => {
    dispatch(
      setMessage({
        message: messages,
        backgroundColor: color,
        fontFamily: fontFamily,
        fontSize: size,
        isDirty: false,
      })
    );
  };

  /**
   * @method onFontSizeUpdated
   */
  const onFontUpdated = (font: string) => {
    dispatch(
      setMessage({
        message: messages,
        backgroundColor: color,
        fontFamily: font,
        fontSize: fontSize,
        isDirty: false,
      })
    );
  };

  /**
   * @method onContinueClicked
   */
  const onContinueClicked = () => {
    setIsLoading(BUTTON_TYPE.CONTINUE)
    router.push(
      `${PAGEURLS.GROUP_GIFTS}/${params?.giftid}${PAGEURLS.CONFIRM}`
    );
  };

  const onSkipClicked = () => {
    setIsLoading(BUTTON_TYPE.SKIP)
    dispatch(
      setMessage({
        message: '',
        backgroundColor: '#EB6B6B',
        fontFamily: 'Mona Sans',
        fontSize: 18,
        isDirty: false,
      })
    );
    router.push(`${PAGEURLS.GROUP_GIFTS}/${params?.giftid}${PAGEURLS.CONFIRM}`);
  };

  const goBack = () => {
    router.replace(
      `${PAGEURLS.GROUP_GIFTS}/${params?.giftid}${PAGEURLS.PERSONALIZE}`
    );
  };

  useEffect(() => {
    setMessages(getMessages?.message && getMessages?.message);
    setFontFamily(getMessages?.fontFamily && getMessages?.fontFamily);
    setColor(getMessages?.backgroundColor && getMessages?.backgroundColor);
    setFontSize(getMessages?.fontSize && getMessages?.fontSize);
  }, [
    getMessages?.message,
    getMessages?.fontFamily,
    getMessages?.backgroundColor,
    getMessages?.fontSize,
  ]);

  let selectedFont: any =
    locale === 'ar' ? 'var(--font-noto-kufi)' : 'var(--font-mona-sans)';
  switch (fontFamily) {
    case FONT_FAMILIES.POPPINS:
      selectedFont = 'var(--font-poppins)';
      break;
    case FONT_FAMILIES.GEORGIA:
      selectedFont = 'Georgia';
      break;
    case FONT_FAMILIES.ARIAL:
      selectedFont = 'Arial';
      break;
    case FONT_FAMILIES.BRADLEY:
      selectedFont = 'Bradley Hand';
      break;
    case FONT_FAMILIES.BRUSH_SCRIPT:
      selectedFont = 'Brush script MT';
      break;
    case FONT_FAMILIES.BLAKA_HOLLOW:
      selectedFont = 'var(--blaka-hollow)';
      break;
    case FONT_FAMILIES.TAJAWAL:
      selectedFont = 'var(--tajawal)';
      break;
    case FONT_FAMILIES.REEM_KUFI:
      selectedFont = 'var(--reem-kufi)';
      break;
    case FONT_FAMILIES.EL_MESSIRI:
      selectedFont = 'var(--el-messiri)';
      break;
    case FONT_FAMILIES.CAIRO:
      selectedFont = `var(--font-cairo)`;
      break;
    case FONT_FAMILIES.MONA_SANS:
      selectedFont = `var(--font-mona-sans)`;
      break;
    case FONT_FAMILIES.NOTA_KUFI:
      selectedFont = `var(--font-noto-kufi)`;
      break;
  }

  useEffect(() => {
    HAS_WINDOWS_REF && window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  return (
    <>
      <ContributionHeader />
      <div className={`${styles['message']}`}>
        <div className={`${styles['message__top-section']} amount-selection`}>
          <div className={styles['message__progress']}>
            <span onClick={goBack}>
              <img src={backButton} alt="img" />
            </span>

            <h4 className={styles['message__title']}>{t('yourMessage')}</h4>
          </div>
          <div>
            <Button
              className={
                isLoading ? styles['message__skip-loader'] : ''
              }
              loader={isLoading == BUTTON_TYPE.SKIP}
              action={onSkipClicked}
              theme="small-grey"
            >
              <span> {t('skip')}</span>
            </Button>
          </div>
        </div>
        <div className={`${styles['message__container']}`}>
          <div className={`${styles['message__display']}`}>
            <textarea
              maxLength={MAX_MESSSAGE_LENGTH}
              onChange={onMessageUpdated}
              className="brand-message-editor"
              data-testid="userMessageText"
              value={messages}
              placeholder={placeHolderText}
              style={{
                backgroundColor: color,
                fontSize: `${fontSize}pt`,
                fontFamily: selectedFont,
              }}
              rows={5}
            />
          </div>
          <div className={`${styles['message__control-panel']}`}>
            <div className={styles['message__control-panel-block']}>
              <BackGroundColorMenu
                selectedColor={color}
                onBackGroundColorUpdated={onBackGroundColorUpdated}
              />
            </div>
            <div className={styles['message__control-panel-block']}>
              <FontFamilyMenu
                onFontUpdated={onFontUpdated}
                locale={locale}
                selectedFont={fontFamily}
              />
            </div>
            <div className={styles['message__control-panel-block']}>
              <FontSizeMenu
                selectedSize={fontSize}
                onFontSizeUpdated={onFontSizeUpdated}
              />
            </div>
          </div>
        </div>
      </div>

      <div className={`${styles['message__btn-wrapper']}`}>
        <Button loader={isLoading == BUTTON_TYPE.CONTINUE} theme="primary" action={onContinueClicked} attribues={{ disabled: isLoading }} className={styles['message__btn-wrapper-continue']}>{t('continue')}</Button>
      </div>
    </>
  );
};

export default YourMessage;
