@import '@/styles/abstracts/abstracts';

.payment-failure {
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  &__main-wraper {
    position: relative;
  }

  &__marquee1 {
    margin: 0 40px;

    &-title {
      margin-top: 0 !important;
      font-size: rem(32) !important;
      color: $dark-charcoal;
      font-family: var(--font-bricolage); font-optical-sizing: none;
      font-weight: 800;
      line-height: 40px;
      letter-spacing: -0.16px;
    }
  }

  &__marquee {
    margin: 0 40px;
    display: flex;
    gap: 5px;
    align-items: center;
    color: $dark-charcoal;
    font-weight: 500;
    margin-top: 8px;
    line-height: 18px;
    letter-spacing: -0.14px;

    &-text {
      width: 131px;
      font-size: rem(14);
      color: $dark-charcoal;
      font-weight: 500 !important;
      line-height: 18px;
      letter-spacing: -0.14px;

      @include rtl-styles {
        width: 155px;
      }
    }
  }

  &__title {
    font-size: rem(32);
    color: $dark-charcoal;
    font-family: var(--font-bricolage); font-optical-sizing: none;
    font-weight: 800;
    line-height: 40px;
    letter-spacing: -0.16px;
  }

  &__organizer-text {
    font-size: rem(14);
    margin-top: 8px;
    color: $dark-charcoal;
    font-weight: 500;
    line-height: 18px;
    letter-spacing: -0.14px;

    span {
      font-weight: 600;
    }
  }

  &__bg-layout {
    text-align: center;
    position: relative;
  }

  &__status-icon {
    margin-top: 24px;
    width: 106px;
    height: 106px;
    z-index: 99;
  }

  &__top-container {
    text-align: center;
  }

  &__message {
    margin-top: rem(24);
    &-rew{
      margin-top: rem(24);
    }
    p:first-child {
      color: $medium-red;
      font-size: rem(24);
      font-weight: 600;
    }

    p:last-child {
      color: $dark-charcoal;
      font-size: rem(32);
      font-weight: 800;
      line-height: 40px;
      letter-spacing: -0.16px;
      font-family: var(--font-bricolage); font-optical-sizing: none;
    }

    &-rewards {
      font-size: 16px;
      margin-top: rem(24);
      font-weight: 500;
      font-weight: normal;
      color: #545454;
      span:last-child {
        font-weight: 600;
        color: $dark-charcoal;
      }
    }
  }

  &__bottom-container {
    z-index: 2;
    position: absolute;
    bottom: 0;
    left: 0;
    padding-inline: 20px;
    width: 100%;
    > p {
      text-align: center;
      color: $dark-charcoal;
      font-size: rem(14);
      font-weight: 600;
      line-height: 24px;
      letter-spacing: -0.14px;
      margin-bottom: 16px;
    }

    .btn-container{
      position: absolute;
      bottom: 0;
      width: 100%;
      left: 0;
      padding: 16px 20px;
      box-shadow: 0px -4px 20px 0px rgba(0, 0, 0, 0.06);
      border-top-left-radius: 20px;
      border-top-right-radius: 20px;
    }
  }

  &__list {
    padding: rem(16) rem(8);
    background-color: rgba(210, 201, 255, 0.25);
    border-radius: 12px;
    margin-top: 64px;

    ul {
      display: flex;
      flex-direction: column;
      gap: 8px;
      padding-left: rem(20);
      list-style-position: inside;
      @include rtl-styles {
        padding-right: rem(20);
      }

      li {
        list-style: disc;
        color: $dark-charcoal;
        font-size: 14px;
        font-weight: 500;
        line-height: 18px;
        letter-spacing: -0.14px;
      }
    }
  }

  &__download-app {
    display: flex;
    justify-content: center;
    margin-bottom: rem(114);
    flex-wrap: wrap;
    gap: 16px;

    img {
      height: rem(48);
      width: rem(48);
      cursor: pointer;
      border-radius: 50%;
    }
  }

  .back-to-Btn {
    width: 100%;
    height: 50px;
    font-size: rem(16);
    padding: rem(13) 0;
    font-weight: 600;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .payment-failure-skelton {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    &__header {
      font-size: rem(18);
      font-weight: 600;
      width: 70%;
    }
    &__sub-header {
      width: 50%;
      font-size: rem(16);
      font-weight: 500;
    }
  }
}

.marquee {
  direction: ltr !important;
  scrollbar-width: none;

  &::-webkit-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
    display: none;
    width: 0px;
  }
}
