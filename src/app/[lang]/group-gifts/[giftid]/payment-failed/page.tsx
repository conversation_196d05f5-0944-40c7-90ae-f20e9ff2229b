'use client';
import React, { useEffect, useState } from 'react';
import styles from './PaymentFailed.module.scss';
import Button from '@/features/common/button/Button';
import { useTranslation } from 'react-i18next';
import { useQuery } from '@apollo/client';
import { DOWNLOAD_APP_QUERY } from '@/features/common/common.query';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { PAGEURLS } from '@/features/gifts/constants/gifts.constants';
import DownloadAppStoresSkelton from '@/features/gifts/contentLoader/paymentSuccessSkelton/DownloadAppStoresSkelton';
import useGiftsAPI from '@/features/gifts/giftsAPI';
import { getCookie } from '@/utils/getAndsetCookie';
import { IS_GUEST_USER_COOKIE } from '@/constants/common';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import useCommonSlice from '@/features/common/commonSlice';
import { Skeleton } from '@mui/material';
import Marquee from 'react-fast-marquee';
import getRegionAndLocale from '@/utils/getRegionAndLocale';
import useContributionSlice from '@/features/gifts/contributionFlowSlice';
import useGuestUserSlice from '@/features/gifts/giftsOpen/guestUserSlice';

export default function PaymentFailure() {
  // translations
  const { t } = useTranslation();
  const params = useParams();
  const referenceId = params?.giftid;
  const searchParams = useSearchParams();
  const transactionId = searchParams.get('transaction_id');
  const isRewards = searchParams.get('embedded');
  const router = useRouter();
  const { locale } = getRegionAndLocale();
  const [loader, setLoader] = useState<boolean>();
  const { getTokenInfo, getGuestInfo } = useCommonSlice();
  const { AccessToken }: any = useAppSelector(getTokenInfo);
  const { GuestAccessToken }: any = useAppSelector(getGuestInfo);
  const isGuestLoginCookie: any = getCookie(IS_GUEST_USER_COOKIE);
  const { HandlePaymentCheckoutVerification } = useGiftsAPI();
  const dispatch = useAppDispatch();

  // #. Clear Redux persisted values
  const { clearState } = useContributionSlice();
  const { clearGuestSlice } = useGuestUserSlice();

  // #.Set payload for payment verify API
  const input = {
    transactionId,
  };

  const { paymentVerificationData, paymentVerificationLoading } =
    HandlePaymentCheckoutVerification(
      input,
      isGuestLoginCookie === 'true' ? GuestAccessToken : AccessToken,
      referenceId,
      isGuestLoginCookie
    );

  // #. Set payment and gift detail data.
  const giftDetailData = paymentVerificationData?.groupGiftDetail;

  //Download app data getting from webstore API
  const { error, data, loading } = useQuery<any>(DOWNLOAD_APP_QUERY, {
    context: {
      clientName: 'webstore-with-cdn',
    },
  });

  // #.Set app store images
  const appBanner = data?.downloadApp[0]?.roundedAppBanner;

  /**
   * @method handleBacktoGroupGift
   * @description redirection to gift detail page
   */
  const handleBacktoGroupGift = () => {
      try {
      setLoader(true);
      router.push(`${PAGEURLS.GIFTS_DETAILS}/${referenceId}`);
      } catch (error) {
        setLoader(false);
      }
  };

  useEffect(() => {
    dispatch(clearState());
    dispatch(clearGuestSlice()); // cleared redux stored values
  }, []);

  return (
    <div className={styles['payment-failure']}>
      <div className={styles['payment-failure__top-container']}>
        <div className={styles['payment-failure__main-wraper']}>
          {!paymentVerificationLoading ? (
            <div
              className={`${styles['payment-failure__bg-layout']}
            ${!!isRewards ? styles['payment-failure__bg-layout-rewards'] : ''}
            `}
            >
              {!isRewards ?
                giftDetailData?.title?.length > 20 ? (
                  <div className={styles['payment-failure__marquee1']}>
                    <Marquee
                      speed={30}
                      className={styles['marquee']}
                      direction={locale === 'ar' ? 'right' : 'left'}
                    >
                      <p className={styles['payment-failure__marquee1-title']}>
                        {giftDetailData?.title}
                      </p>
                      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    </Marquee>
                  </div>
              ) : (
                <p className={styles['payment-failure__title']}>
                  {giftDetailData?.title}
                </p>
              ) : ""}
              
              {!!isRewards ? (
              <img src="/images/ygag-logo.png" width={59} height={40} />
            ) :
              giftDetailData?.organizerName?.length > 25 ? (
                <div className={styles['payment-failure__marquee']}>
                  <span className={styles['payment-failure__marquee-text']}>
                    {t('organizedByText')}
                  </span>
                  <Marquee
                    speed={30}
                    className={styles['marquee']}
                    direction={locale === 'ar' ? 'right' : 'left'}
                  >
                    <span>{giftDetailData?.organizerName}</span>
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                  </Marquee>
                </div>
              ) : (
                <p className={styles['payment-failure__organizer-text']}>
                  {t('organizedByText')}{' '}
                  <span>{giftDetailData?.organizerName}</span>
                </p>
              )}
            </div>
          ) : (
            <div className={styles['payment-failure__bg-layout']}>
              <div className={styles['payment-failure-skelton']}>
                <Skeleton
                  className={styles['payment-failure-skelton__header']}
                />
                <Skeleton
                  className={styles['payment-failure-skelton__sub-header']}
                />
              </div>
            </div>
          )}
          <img
            className={`${styles['payment-failure__status-icon']}
            ${!!isRewards ? styles["payment-failure__status-icon-rewards"] : ""}
            `}
            src="/images/icons/close-circle.png"
            alt=""
          />
        </div>
        <div className={`${styles['payment-failure__message']}
        ${!!isRewards ? styles["payment-failure__message-rew"] : ""}
        `}>
          <p>{t('paymentFailed')}</p>
        </div>
        {!!isRewards && (
            <div className={styles['payment-failure__message-rewards']}>
              <span>{t('for')}</span>&nbsp;<span>{giftDetailData?.title}</span>
            </div>
          )}
      </div>

      <div className={styles['payment-failure__list']}>
          <ul>
            <li>{t('tryContributeAgain')}</li>
            <li>{t('networkConnectionError')}</li>
            <li>{t('contributeAgain')}</li>
          </ul>
        </div>

      <div className={styles['payment-failure__bottom-container']}>
        <p>{t('downloadApp')}</p>
        <div className={styles['payment-failure__download-app']}>
          {!loading ? (
            appBanner?.map((item: any, index: number) => (
              <a 
                href={!!isRewards ? undefined : item?.itemUrl}
                onClick={(e) => {
                  if (!!isRewards) {
                    e.preventDefault(); // Prevent the default href behavior in iframe
                    if (window.top) {
                      window.top.location.href = item?.itemUrl; // Redirect using window.top
                    }
                  }
                  // No else needed, as href will handle the redirection when not in iframe
                }}
                key={index}>
                <img src={item?.itemImage} alt={item?.itemName} />
              </a>
            ))
          ) : (
            <DownloadAppStoresSkelton />
          )}
        </div>
        <div className={styles['btn-container']}>
        <Button
          action={handleBacktoGroupGift}
          theme="primary"
          className={styles['back-to-Btn']}
          loader={loader}
        >
          {t('done')}
        </Button>
        </div>
      </div>
    </div>
  );
}
