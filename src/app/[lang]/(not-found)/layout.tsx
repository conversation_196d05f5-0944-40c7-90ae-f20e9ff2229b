import '@/styles/style.scss';
import type { Metadata } from 'next';
import FooterServer from '@/features/common/footer/FooterServer';
import { FOOTER_TYPE } from '@/constants/common';
import AuthenticationProviderServer from '@/features/common/auth/AuthenticationProviderServer';
import { getUserInfo } from '@/utils/authInfo';
import TabSelectionHeaderServer from '@/features/common/header/tabSelectionHeader/TabSelectionHeaderServer';


export const metadata: Metadata = {
  title: 'YOUGotaGift.com | Group Gifts',
  description: 'YOUGotaGift.com Group Gifting - The easiest way to organise a Group Gift and invite your friends to contribute!',
  keywords: 'gift,gifts,gifting,group gifting,weddings,birthdays,baby showers,wedding registry,group raise money'
};

export default async function NotFoundPageLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // #. Get token info API (server side) call
  const userInfo = await getUserInfo();

  return (
    <>
      <AuthenticationProviderServer>
        <TabSelectionHeaderServer userInfo={userInfo}>
          <section>{children}</section>
        </TabSelectionHeaderServer>
        <FooterServer type={FOOTER_TYPE.HOME} />
      </AuthenticationProviderServer>
    </>
  );
}
