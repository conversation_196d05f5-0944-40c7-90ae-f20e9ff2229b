'use client';
import React from 'react';
import { imageBaseUrl } from '@/constants/envVariables';
import { useRouter } from 'next/navigation';
import { useTranslation } from 'react-i18next';
import Button from '@/features/common/button/Button';
import getRegionAndLocale from '@/utils/getRegionAndLocale';

const page = () => {
  // translations
  const { t } = useTranslation();
  const router = useRouter();
  const { locale, region } = getRegionAndLocale();
  const store = `${locale}-${region}`;

  /**
   * @method onGotoHomeClicked
   * @description Handle goto home button click - navigate to home page
   */
  const onGotoHomeClicked = () => {
    router.push('/');
  };

  return (
    <div className="error-page">
      <h2>{t('errorTitle')}</h2>
      <h5>{t('errorSubTitle')}</h5>
      <img
        data-testid="errorIcon"
        src={`${imageBaseUrl}/images/icons/error.svg`}
        height={164}
        width={135}
        alt="Error"
      />
      <Button theme="secondary" className="error-goto-home" action={onGotoHomeClicked}>
        {t('errorButtonTitle')}
      </Button>
      <span className="error-message" data-testid="errorMessage">
        {t('404')}
      </span>
    </div>
  );
};

export default page;
