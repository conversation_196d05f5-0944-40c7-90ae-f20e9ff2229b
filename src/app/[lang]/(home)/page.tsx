import { getUserInfo } from '@/utils/authInfo';
import HomePageLayout from '@/features/home/<USER>/HomePageLayout';
import FooterServer from '@/features/common/footer/FooterServer';
import { FOOTER_TYPE, LOCALE_REGION_COOKIE } from '@/constants/common';
import TabSelectionHeaderServer from '@/features/common/header/tabSelectionHeader/TabSelectionHeaderServer';
import { cache, Fragment, Suspense } from 'react';
import { getClient } from '@/graphql/apolloClient';
import { LANDING_CONFIG } from '@/features/home/<USER>';
import getCookie from '@/utils/getCookie';
import Loader from '@/features/common/loader/Loader';
import { HomePageSkeleton } from '@/features/home/<USER>';
import HomeDownloadApp from '@/features/home/<USER>/HomeDownloadApp';

export default async function Page({
  params: { lang },
  searchParams,
}: Readonly<{
  params: { lang: any };
  searchParams: { embedded: string };
}>) {
  // #. Check for rewards
  const isRewards = searchParams?.embedded;
  // #. Get token info API (server side) call
  const userInfo = await getUserInfo();

  // Get locale and region from cookie
  const localeRegionCookie: any = getCookie(LOCALE_REGION_COOKIE);
  const localeRegion = localeRegionCookie?.value || 'en-ae';
  const region = localeRegion.split('-')[1];
  const locale = localeRegion.split('-')[0];

  /**
   * @method fetchlandingConfig
   * @returns landing page data
   */
  // Use React cache to prevent duplicate fetches during server rendering
  const fetchlandingConfig = cache(async () => {
    try {
      const response = await getClient(locale).query({
        query: LANDING_CONFIG,
        context: {
          headers: {
            'Accept-Language': locale,
          },
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching site config:', error);
      return null;
    }
  });

  const landingConfig = await fetchlandingConfig();

  return (
    <Suspense
      fallback={<HomePageSkeleton isLoggedIn={userInfo?.isUserSignedIn} />}
    >
      <main>
        {isRewards ? (
          <HomePageLayout userInfo={userInfo} landingConfig={landingConfig} />
        ) : (
            <Fragment>
              <TabSelectionHeaderServer userInfo={userInfo}>
                <HomePageLayout userInfo={userInfo} landingConfig={landingConfig} />
              </TabSelectionHeaderServer>
              <HomeDownloadApp locale={locale} />
              <FooterServer type={FOOTER_TYPE.HOME} />
            </Fragment>
        )}
      </main>
    </Suspense>
  );
}
