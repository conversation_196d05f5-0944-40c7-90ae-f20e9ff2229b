import '@/styles/style.scss';
import 'swiper/scss';
import 'swiper/scss/navigation';
import AuthenticationProviderServer from '@/features/common/auth/AuthenticationProviderServer';

//#. import homepage components styles
import '@/features/home/<USER>/HomeBanner.module.scss';
import '@/features/home/<USER>/GGStepSection.module.scss';
import '@/features/home/<USER>/HappyYouCard.module.scss';
import '@/features/home/<USER>/HomeDownloadApp.module.scss';
import '@/features/common/button/Button.module.scss';
import '@/features/home/<USER>/swiperSkeleton/SwiperSkeleton.module.scss';
import '@/features/home/<USER>/happyYouCardSkelton/HappyYouCardSkelton.module.scss';
import '@/features/home/<USER>/homePageSkeleton/HomePageSkeleton.module.scss';
import '@/features/home/<USER>/bannerSkelton/BannerSkelton.module.scss';
import '@/features/home/<USER>/ggStepSectionSkelton/GGStepSectionSkelton.module.scss';
import '@/features/home/<USER>/downloadAppSkelton/DownloadAppSkeleton.module.scss';
import '@/features/home/<USER>/occasionSectionSkeleton/OccasionSectionSkeleton.module.scss';
import '@/features/common/header/tabSelectionHeader/TabSelectionHeaderSkeleton.module.scss';
import '@/features/home/<USER>/GiftsBanner.module.scss';


export default async function HomePageLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // Authentication is now handled by the server component

  return (
      <AuthenticationProviderServer useSkeletonLoader={true}>
        <section>{children}</section>
      </AuthenticationProviderServer>
  );
}
