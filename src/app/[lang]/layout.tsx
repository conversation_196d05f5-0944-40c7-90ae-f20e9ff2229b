/**
 * 'force-dynamic': Force dynamic rendering, which will result in routes being rendered for each user at request time. This option is equivalent to getServerSideProps() in the pages directory.
 */
export const dynamic = 'force-dynamic';
import '@/styles/style.scss';
import { ApolloWrapper } from '@/graphql/ApolloWrapper';
import ThemeRegistry from '@/features/common/themeRegistry/ThemeRegistry';
import 'swiper/scss';
import 'swiper/scss/navigation';
import 'swiper/scss/grid';
import i18nConfig from '../../../i18nConfig';
import { dir } from 'i18next';
import {
  poppins,
  cairo,
  tajawal,
  elMessiri,
  reemKufi,
  blakaHollow,
  bricolageGrotesque,
  notoKufiArabic,
  monaSans,
} from './../font';
import TranslationsProvider from '@/features/common/i18n/TranslationsProvider';
import initTranslations from '../i18n';
import StoreProvider from '../StoreProvider';
import ConfigureAmplifyClientSide from '@/constants/configureAmplifyClientSide';
import {
  GTM_ID,
  CLEVERTAP_ACCOUNT_ID,
  HOTJAR_ID,
  imageBaseUrl,
} from '@/constants/envVariables';
import Clevertap from '@/features/common/clevertap/Clevertap';
import CheckCookie from '@/features/common/checkCookie/CheckCookie';
import { generateMetadata } from '@/utils/generateMetadata';

export function generateStaticParams() {
  return i18nConfig.locales.map((locale) => ({ locale }));
}
const i18nNamespaces = ['common'];

export { generateMetadata };
export default async function RootLayout({
  children,
  params: { lang },
}: Readonly<{
  children: React.ReactNode;
  params: { lang: any };
}>) {
  const { resources } = await initTranslations(lang, i18nNamespaces);

  return (
    <TranslationsProvider
      namespaces={i18nNamespaces}
      locale={lang}
      resources={resources}
    >
      <StoreProvider>
        <html
          lang={lang}
          dir={dir(lang)}
          className={`${cairo.variable} ${poppins.variable} ${tajawal.variable} ${elMessiri.variable} ${reemKufi.variable} ${blakaHollow.variable} ${bricolageGrotesque.variable} ${notoKufiArabic.variable} ${monaSans.variable}`}
        >
          <head>
            <meta
              name="google-site-verification"
              content="ju1XJiTf2ziduFZgTilgIyg0w6sXRGUPn_34Yc8NMc8"
            />
            {GTM_ID && (
              <script
                dangerouslySetInnerHTML={{
                  __html: `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
                })(window,document,'script','dataLayer','${GTM_ID}');`,
                }}
              />
            )}
            {HOTJAR_ID && (
              <script
                dangerouslySetInnerHTML={{
                  __html: `(function(h,o,t,j,a,r){ h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
                   h._hjSettings={hjid:${HOTJAR_ID},hjsv:6}; a=o.getElementsByTagName('head')[0];
                   r=o.createElement('script');r.async=1;
                   r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv; a.appendChild(r); 
                   })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');`,
                }}
              />
            )}
          </head>
          <body>
            <ThemeRegistry>
              <ApolloWrapper locale={lang}>
                <CheckCookie>
                  <ConfigureAmplifyClientSide />
                  {CLEVERTAP_ACCOUNT_ID && (
                    <Clevertap clevertapAccountId={CLEVERTAP_ACCOUNT_ID} />
                  )}
                  <section>{children}</section>
                </CheckCookie>
              </ApolloWrapper>
            </ThemeRegistry>
          </body>
        </html>
      </StoreProvider>
    </TranslationsProvider>
  );
}
