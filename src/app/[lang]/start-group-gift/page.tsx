import React from 'react';
import dynamic from 'next/dynamic';
import StartGroupGiftLayoutSkelton from '@/features/startGroupGift/contentLoader/startGroupGiftLayoutSkelton/StartGroupGiftLayoutSkelton';
import TranslationsProvider from '@/features/common/i18n/TranslationsProvider';
import FooterServer from '@/features/common/footer/FooterServer';
import initTranslations from '@/app/i18n';
import { getUserInfo } from '@/utils/authInfo';
import TabSelectionHeaderServer from '@/features/common/header/tabSelectionHeader/TabSelectionHeaderServer';


const StartGroupGiftLayout = dynamic(
  () =>
    import(
      '@/features/startGroupGift/startGroupGiftLayout/StartGroupGiftLayout'
    ),
  {
    loading: () => <StartGroupGiftLayoutSkelton />,
    ssr: false,
  }
);

const i18nNamespaces = ['common'];

export default async function Page({
  params: { lang },
  searchParams
}: Readonly<{
  params: { lang: any };
  searchParams: { embedded: string};
}>) {
  // #. Check for rewards
  const isRewards = searchParams?.embedded;

  // #. Get token info API (server side) call
  const userInfo = await getUserInfo();

  const { resources } = await initTranslations(lang, i18nNamespaces);
  return (
    <TranslationsProvider
      namespaces={i18nNamespaces}
      locale={lang}
      resources={resources}
    >
      {isRewards ? (
        <StartGroupGiftLayout />
      ) : (
        <TabSelectionHeaderServer userInfo={userInfo} disableStoreSelection={true}>
          <StartGroupGiftLayout />
          <FooterServer />
        </TabSelectionHeaderServer>
      )}
    </TranslationsProvider>
  );
}
