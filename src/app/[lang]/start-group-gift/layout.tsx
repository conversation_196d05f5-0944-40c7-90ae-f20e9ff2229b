import '@/styles/style.scss';
import type { Metadata } from 'next';
import AuthenticationProviderServer from '@/features/common/auth/AuthenticationProviderServer';

export const metadata: Metadata = {
  title: 'YOUGotaGift.com | Group Gifts',
  description: 'YOUGotaGift.com Group Gifting - The easiest way to organise a Group Gift and invite your friends to contribute!',
  keywords: 'gift,gifts,gifting,group gifting,weddings,birthdays,baby showers,wedding registry,group raise money'
};

export default async function StartGroupGiftPageLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  // Authentication is now handled by the server component

  return (
      <AuthenticationProviderServer>
        <section>{children}</section>
      </AuthenticationProviderServer>
  );
}
