import {
  Poppins,
  Cairo,
  <PERSON><PERSON>_<PERSON>,
  El<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>laka_Hollow,
  Bricolage_Grotesque,
  Noto_<PERSON>_Arabic,
} from 'next/font/google';
import localFont from 'next/font/local'

export const poppins = Poppins({
  weight: ['400', '500', '600', '700'],
  style: ['normal', 'italic'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-poppins',
});

export const cairo = Cairo({
  weight: ['400', '500', '600', '700'],
  style: ['normal'],
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-cairo',
});

export const reemKufi = Reem_Kufi({
  style: ['normal'],
  subsets: ['arabic'],
  display: 'swap',
  variable: '--reem-kufi',
});

export const elMessiri = El_Messiri({
  style: ['normal'],
  subsets: ['arabic'],
  display: 'swap',
  variable: '--el-messiri',
});

export const tajawal = <PERSON><PERSON>wal({
  weight: ['400', '500'],
  style: ['normal'],
  subsets: ['arabic'],
  display: 'swap',
  variable: '--taja<PERSON>',
});

export const blakaHollow = Blaka_Hollow({
  weight: ['400'],
  style: ['normal'],
  subsets: ['arabic'],
  display: 'swap',
  variable: '--blaka-hollow',
});

// Bricolage Grotesque
export const bricolageGrotesque = Bricolage_Grotesque({
  // weight: ['400', '500', '600', '700', '800'],
  weight: 'variable',
  style: ['normal'],
  subsets: ['latin'],
  display: 'swap',
  axes: ["opsz"],
  variable: '--font-bricolage',
});

// Noto Kufi Arabic
export const notoKufiArabic = Noto_Kufi_Arabic({
  weight: ['400', '500', '600', '700', '800'],
  subsets: ['arabic'],
  display: 'swap',
  variable: '--font-noto-kufi',
});

// Mona Sans
export const monaSans = localFont({
  src:[ {
    path: './fonts/mona-sans/Mona-Sans-Regular.ttf',
    weight: '400',
    style: 'normal',
  },
  {
    path: './fonts/mona-sans/Mona-Sans-Medium.ttf',
    weight: '500',
    style: 'normal',
  },
  {
    path: './fonts/mona-sans/Mona-Sans-SemiBold.ttf',
    weight: '600',
    style: 'normal',
  },
  {
    path: './fonts/mona-sans/Mona-Sans-Bold.ttf',
    weight: '700',
    style: 'normal',
  },
  {
    path: './fonts/mona-sans/Mona-Sans-ExtraBold.ttf',
    weight: '800',
    style: 'normal',
  }],
  display: 'swap',
  variable: '--font-mona-sans'
})