import { DOMAIN } from '@/constants/envVariables';

export async function POST(request: any) {
  // Parse the readable stream to get the request body
  const buffers = [];
  for await (const chunk of request?.body) {
    buffers.push(chunk);
  }
  const bodyString = Buffer.concat(buffers).toString();
  const body = JSON.parse(bodyString);

  // Access the parsed body
  const { type } = body;
  const token = type == "REW" ? 'groupgift_rewards_refresh_token' : 'REFRESH_TOKEN';

  const headers = [
    `${token}=deleted; path=/; domain=${DOMAIN}; expires=Thu, 01 Jan 1970 00:00:00 GMT;`
  ];
  // Set the rewards-clear cookie only if type is "REW"
  if (type === "REW") {
    headers.push(`rewards-clear=true; path=/; domain=${DOMAIN};`);
  }

  return new Response(JSON.stringify({ message: `${token} cookie removed ${type === "REW" && 'and set rewards-clear cookie'}` }), {
    status: 200,
    headers: {
      'Set-Cookie': headers.join(', ')
    }
  })
}