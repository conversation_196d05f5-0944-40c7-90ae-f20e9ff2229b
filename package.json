{"name": "ecom_groupgift_frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "npm run sprites && next dev", "build": "npm run sprites && next build", "start": "next start", "start:local": "npm run sprites && PORT=3002 node ./server-local.js", "lint": "next lint", "sprites": "svg-sprite-generate -d public/images/icons/ -o public/images/sprite.svg"}, "dependencies": {"@apollo/client": "^3.9.5", "@apollo/react-testing": "^4.0.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@giphy/js-fetch-api": "^5.2.0", "@giphy/react-components": "^9.2.3", "@mui/icons-material": "^5.15.1", "@mui/material": "^5.15.1", "@mui/x-date-pickers": "^5.0.0-beta.6", "@react-native-async-storage/async-storage": "^1.21.0", "@reduxjs/toolkit": "^1.9.7", "@sentry/cli": "^2.32.1", "@sentry/nextjs": "^8.31.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.1.0", "@testing-library/user-event": "^14.5.1", "@types/gsap": "^3.0.0", "@yougotagift/personalization_preview_package_frontend": "npm:@YouGotaGift/personalization_preview_package_frontend@^1.0.8", "apollo-link-logger": "^2.0.1", "autoprefixer": "^10.4.16", "aws-amplify": "^4.3.27", "clevertap-web-sdk": "^1.1.2", "crypto": "^1.0.1", "crypto-js": "^4.1.1", "date-fns": "^2.30.0", "framer-motion": "^11.0.8", "graphql": "^16.8.1", "gsap": "^3.12.5", "i18next": "^23.7.8", "i18next-resources-to-backend": "^1.2.0", "isomorphic-dompurify": "^1.6.0", "isomorphic-fetch": "^3.0.0", "lodash": "^4.17.21", "next": "14.0.2", "next-client-cookies": "^1.1.0", "next-i18n-router": "^5.0.2", "next-i18next": "^15.0.0", "next-redux-cookie-wrapper": "^2.2.1", "next-redux-wrapper": "^8.1.0", "photoeditorsdk": "^5.19.0", "react": "^18", "react-awesome-reveal": "^4.2.7", "react-dom": "^18", "react-fast-marquee": "^1.6.4", "react-i18next": "^13.5.0", "react-infinite-scroll-component": "^6.1.0", "react-lazy-load-image-component": "^1.6.0", "react-marquee-slider": "^1.1.5", "react-phone-number-input": "^3.3.7", "react-redux": "^8.1.3", "redux-persist": "^6.0.0", "redux-thunk": "^2.4.2", "sass": "^1.69.5", "sharp": "^0.33.2", "styled-components": "^6.1.3", "svg-sprite-generator": "^0.0.7", "swiper": "^11.0.5", "twemoji": "^14.0.2", "uuid": "^8.3.2"}, "devDependencies": {"@types/clevertap-web-sdk": "^1.1.0", "@types/isomorphic-fetch": "^0.0.39", "@types/lodash": "^4.17.7", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-lazy-load-image-component": "^1.6.3", "@types/uuid": "^8.3.4", "eslint": "^8", "eslint-config-next": "14.0.2", "husky": "^8.0.3", "jest": "^29.7.0", "postcss": "^8.4.31", "postcss-rtlcss": "^4.0.9", "typescript": "^5"}}