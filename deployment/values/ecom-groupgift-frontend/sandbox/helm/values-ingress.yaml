ingress:
  apiVersion: 'networking.k8s.io/v1'

  namespace: 'ecom-groupgift-frontend'
  name: 'ecom-groupgift-frontend-ingress'

  annotations:
    alb.ingress.kubernetes.io/ssl-redirect: '443'
    alb.ingress.kubernetes.io/backend-protocol: 'HTTP'
    alb.ingress.kubernetes.io/certificate-arn: '[CERTIFICATE_ARN]'
    alb.ingress.kubernetes.io/group.name: 'default'
    alb.ingress.kubernetes.io/healthcheck-path: '/health/'
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS": 443}]'
    alb.ingress.kubernetes.io/scheme: 'internet-facing'
    alb.ingress.kubernetes.io/tags: 'Name=ygag-sandbox-default-alb-eks-tf, Platform=EKS, Environment=sandbox'
    alb.ingress.kubernetes.io/wafv2-acl-arn: '[WAF_ARN]'
    alb.ingress.kubernetes.io/target-type: 'ip'
    alb.ingress.kubernetes.io/subnets: 'ygag-sandbox-default-public-ap-south-1a-tf, ygag-sandbox-default-public-ap-south-1b-tf, ygag-sandbox-default-public-ap-south-1c-tf'
    alb.ingress.kubernetes.io/ssl-policy: 'ELBSecurityPolicy-TLS-1-2-2017-01'
    alb.ingress.kubernetes.io/load-balancer-attributes: 'access_logs.s3.enabled=true,access_logs.s3.bucket=[ALB_ACCESS_LOG_BUCKET],access_logs.s3.prefix=default-public,idle_timeout.timeout_seconds=120'
    kubernetes.io/ingress.class: 'alb'

  rules:
    - host: '[DOMAIN]'
      http:
        paths:
          - backend:
              service:
                name: 'ecom-groupgift-frontend-nginx'
                port:
                  number: 80
            path: '/*'
            pathType: 'ImplementationSpecific'
