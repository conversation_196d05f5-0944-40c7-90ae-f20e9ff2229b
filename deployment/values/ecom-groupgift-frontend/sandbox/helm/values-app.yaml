namespace: 'ecom-groupgift-frontend'

app_name: 'ecom-groupgift-frontend'
environment: 'sandbox'

service:
  name: 'ecom-groupgift-frontend-app'
  default:
    port: 3000
    protocol: 'TCP'
    targetPort: 3000

hpa:
  name: 'ecom-groupgift-frontend-app-hpa'
  minReplicas: 3
  maxReplicas: 5
  targetCPUUtilizationPercentage: 80

pdb:
  name: 'ecom-groupgift-frontend-app-pdb'
  minAvailable: 50%

deployment:
  name: 'ecom-groupgift-frontend-app-deployment'
  replicas: 3
  maxSurge: 100%
  maxUnavailable: 0%
  serviceAccountName: 'ygag-ecom-groupgift-frontend-vault'

  containers:
    default:
      name: 'app'
      imagePullPolicy: 'Always'
      image: '************.dkr.ecr.ap-south-1.amazonaws.com/production/ygag/ecom-groupgift/frontend-app:[BUILD_TAG]'
      command: '["sh", "-c" , "source /vault/secrets/application.env; node server.js"]'
      port: 3000
      memory:
        requests: 400Mi
        limits: 500Mi
      cpu:
        requests: 100m
      health:
        path: '/health/'
        port: 3000
        scheme: 'HTTP'
      startupProbe:
        initialDelaySeconds: 10
        periodSeconds: 5
        timeoutSeconds: 5
        successThreshold: 1
        failureThreshold: 7
      readinessProbe:
        initialDelaySeconds: 0
        periodSeconds: 15
        timeoutSeconds: 5
        successThreshold: 1
        failureThreshold: 3
      livenessProbe:
        initialDelaySeconds: 0
        periodSeconds: 15
        timeoutSeconds: 5
        successThreshold: 1
        failureThreshold: 4
      volumeMounts:
        - mountPath: '/ygag/logs/'
          name: 'ygag-ecom-groupgift-frontend-sandbox-app-logs'
        - name: 'ecom-groupgift-frontend-app-env-volume'
          mountPath: "/vault/secrets"

  volumes:
    - name: 'ygag-ecom-groupgift-frontend-sandbox-app-logs'
      hostPath:
        path: '/home/<USER>/ygag-logs/ygag-ecom-groupgift-frontend-sandbox/app'
    - name: 'ecom-groupgift-frontend-app-env-volume'
      csi:
        driver: secrets-store.csi.k8s.io
        readOnly: true
        volumeAttributes:
          secretProviderClass: "ecom-groupgift-frontend-envs"

  nodeSelector:
    key: 'karpenter.sh/provisioner-name'
    value: 'default'

  topologySpreadConstraints:
    - maxSkew: 1
      topologyKey: 'topology.kubernetes.io/zone'
      whenUnsatisfiable: DoNotSchedule
      labelSelector:
        matchLabels:
          app: 'ecom-groupgift-frontend'
          tier: app

  priorityClassName: 'sandbox-medium'
  terminationGracePeriodSeconds: 60
