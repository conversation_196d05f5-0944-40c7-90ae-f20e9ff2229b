# Variables for namespace creation helm releases
application_namespace_charts = [
  # namespace
  {
    create_helm_release = true

    namespace        = "ecom-groupgift-frontend"
    create_namespace = false
    repository       = "oci://459037613883.dkr.ecr.us-east-1.amazonaws.com/production/helm/ygag"
    release_name     = "ecom-groupgift-frontend-[JIRA_ID]-namespace"
    chart            = "namespace"
    version          = "1.0.1"
    wait             = true
    atomic           = true
    timeout          = 600
    values_file_name = "values-namespace.yaml"
  },
]

# Variables for access control helm releases
application_access_control_charts = [
  # users
  {
    create_helm_release = true

    namespace        = "ecom-groupgift-frontend-[JIRA_ID]"
    create_namespace = false
    repository       = "oci://459037613883.dkr.ecr.us-east-1.amazonaws.com/production/helm/ygag"
    release_name     = "ecom-groupgift-frontend-[JIRA_ID]-users"
    chart            = "users"
    version          = "1.0.0"
    wait             = true
    atomic           = true
    timeout          = 600
    values_file_name = "values-users.yaml"
  },
]
# Variables for K8s secret provider class
application_secret_provider_class_charts = [
  # ecom-groupgift-frontend-envs
  {
    create_helm_release = true

    namespace        = "ecom-groupgift-frontend-[JIRA_ID]"
    create_namespace = false
    repository       = "oci://459037613883.dkr.ecr.us-east-1.amazonaws.com/production/helm/ygag"
    release_name     = "ecom-groupgift-frontend-[JIRA_ID]-spc"
    chart            = "secret-provider-class"
    version          = "1.0.2"
    wait             = true
    atomic           = true
    timeout          = 600
    values_file_name = "values-secret-provider-class.yaml"
  },
]

# variable for application helm releases
application_charts = [
  # app
  {
    create_helm_release = true

    namespace        = "ecom-groupgift-frontend-[JIRA_ID]"
    create_namespace = false
    repository       = "oci://459037613883.dkr.ecr.us-east-1.amazonaws.com/production/helm/ygag"
    release_name     = "ecom-groupgift-frontend-[JIRA_ID]-app"
    chart            = "app"
    version          = "1.1.0"
    timeout          = 600
    atomic           = true
    cleanup_on_fail  = true
    values_file_name = "values-app.yaml"
  },
  # ingress
  {
    create_helm_release = true

    namespace        = "ecom-groupgift-frontend-[JIRA_ID]"
    create_namespace = false
    repository       = "oci://459037613883.dkr.ecr.us-east-1.amazonaws.com/production/helm/ygag"
    release_name     = "ecom-groupgift-frontend-[JIRA_ID]-ingress"
    chart            = "ingress"
    version          = "1.0.0"
    timeout          = 600
    atomic           = true
    cleanup_on_fail  = true
    values_file_name = "values-ingress.yaml"
  },
  # nginx
  {
    create_helm_release = true

    namespace        = "ecom-groupgift-frontend-[JIRA_ID]"
    create_namespace = false
    repository       = "oci://459037613883.dkr.ecr.us-east-1.amazonaws.com/production/helm/ygag"
    release_name     = "ecom-groupgift-frontend-[JIRA_ID]-nginx"
    chart            = "nginx"
    version          = "1.1.0"
    timeout          = 600
    atomic           = true
    cleanup_on_fail  = true
    values_file_name = "values-nginx.yaml"
  },
]

# variables for application data resource finding created load balancer in above step
application_ingress = {
  name      = "ecom-groupgift-frontend-[JIRA_ID]-ingress"
  namespace = "ecom-groupgift-frontend-[JIRA_ID]"
}


# variables for application application route 53
application_route53 = {
  create_record = true

  name    = "ecom-groupgift-frontend-[JIRA_ID].sit.yougotagift.co"
  zone_id = "Z072540022LEC2L6IO1NI"
  type    = "A"
}

