namespace: 'ecom-groupgift-frontend-[JIRA_ID]'

app_name: 'ecom-groupgift-frontend-[JIRA_ID]'
environment: 'qa'

service:
  name: 'ecom-groupgift-frontend-[JIRA_ID]-nginx'
  default:
    port: 80
    protocol: 'TCP'
    targetPort: 80
  https:
    port: 443
    protocol: 'TCP'
    targetPort: 80

enableHPA: false
hpa:
  name: 'ecom-groupgift-frontend-[JIRA_ID]-nginx-hpa'
  minReplicas: 2
  maxReplicas: 2
  targetCPUUtilizationPercentage: 80

enableKedaCron: true
keda:
  name: 'ecom-groupgift-frontend-[JIRA_ID]-nginx-keda'
  minReplicas: 1
  desiredReplicas: 2
  maxReplicas: 2
  start: "0 7 * * 1-5"
  end: "0 22 * * 1-5"
  cpu: 80

pdb:
  name: 'ecom-groupgift-frontend-[JIRA_ID]-nginx-pdb'
  minAvailable: 0%

deployment:
  name: 'ecom-groupgift-frontend-[JIRA_ID]-nginx-deployment'
  replicas: 2
  maxSurge: 100%
  maxUnavailable: 50%

  containers:
    default:
      name: 'nginx'
      imagePullPolicy: 'IfNotPresent'
      image: '420360167813.dkr.ecr.me-central-1.amazonaws.com/qa/ygg/ecom-groupgift/frontend-nginx:[BUILD_TAG]'
      command: '["sh", "-c", "sed -i \"s/\\[JIRA_ID\\]/[JIRA_ID]/g\" /ygag/nginx/conf/nginx.conf ; sh /ygag/nginx/entrypoint.sh "]'
      port: 80
      memory:
        requests: 20Mi
        limits: 26Mi
      cpu:
        requests: 10m
        limits: 13m
      health:
        path: '/nginx-health'
        port: 80
        scheme: 'HTTP'
      startupProbe:
        initialDelaySeconds: 3
        periodSeconds: 5
        timeoutSeconds: 2
        successThreshold: 1
        failureThreshold: 5
      readinessProbe:
        initialDelaySeconds: 0
        periodSeconds: 5
        timeoutSeconds: 2
        successThreshold: 1
        failureThreshold: 3
      livenessProbe:
        initialDelaySeconds: 0
        periodSeconds: 5
        timeoutSeconds: 2
        successThreshold: 1
        failureThreshold: 4

  volumes:
    local:
      - name: 'ygag-ecom-groupgift-frontend-[JIRA_ID]-qa-nginx-logs'
        mountPath: '/var/log/nginx/'
        hostPath: '/home/<USER>/ygag-logs/ygag-ecom-groupgift-frontend-[JIRA_ID]-qa/nginx'

  initContainers:
    app:
      - image: '420360167813.dkr.ecr.me-central-1.amazonaws.com/qa/k8s/busybox:latest'
        host: 'ecom-groupgift-frontend-[JIRA_ID]-app'
        name: 'init-app'
        port: 3000
        healthPath: '/health/'

  nodeSelector:
    key: 'karpenter.sh/nodepool'
    value: 'default'

  topologySpreadConstraints:
    - maxSkew: 2
      topologyKey: 'topology.kubernetes.io/zone'
      whenUnsatisfiable: DoNotSchedule
      labelSelector:
        matchLabels:
          app: 'ecom-groupgift-frontend-[JIRA_ID]'
          tier: nginx
    - maxSkew: 2
      topologyKey: 'kubernetes.io/hostname'
      whenUnsatisfiable: DoNotSchedule
      labelSelector:
        matchLabels:
          app: 'ecom-groupgift-frontend-[JIRA_ID]'
          tier: nginx

  priorityClassName: 'qa-medium'
  terminationGracePeriodSeconds: 100
