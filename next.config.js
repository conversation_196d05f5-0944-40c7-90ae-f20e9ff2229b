/** @type {import('next').NextConfig} */
const { withSentryConfig } = require("@sentry/nextjs");
const isProd = process.env.NODE_ENV === 'production';


const REWARDS_APP_URL = "https://rewards-rew-1401.sit.yougotagift.co/";
const REWARDS_DEMO_URL = "https://rewards-demo.sit.yougotagift.co/";
const ECOM_WEB_URL = "https://ecom-frontend-ev-ecomv2.sit.yougotagift.co/"
const ECOM_WEB_DEMO_URL = "https://ecom-frontend-ev-ecomv2.sit.yougotagift.co/"



// You can choose which headers to add to the list
const securityHeaders = [
  {
    key: 'X-Frame-Options',
    value: 'SAMEORIGIN'
  },
  {
    key: 'Content-Security-Policy',
    value:
      `frame-ancestors 'self' ${process.env.NEXT_PUBLIC_ENVIRONMENT === "qa" ? `${REWARDS_APP_URL} ${REWARDS_DEMO_URL} ${ECOM_WEB_URL} ${ECOM_WEB_DEMO_URL}` : `${process.env.NEXT_PUBLIC_REWARDS_APP_URL} ${process.env.NEXT_PUBLIC_REWARDS_APP_SA_URL} ${process.env.NEXT_PUBLIC_REWARDS_APP_OCI_SA_URL} ${process.env.NEXT_PUBLIC_ECOM_WEB_URL}`};`,
  },
]

const nextConfig = {
  assetPrefix: isProd ? process.env.NEXT_PUBLIC_GROUPGIFT_ASSET_PREFIX : '',
  output: 'standalone',
  reactStrictMode: false,
  modularizeImports: {
    '@mui/icons-material': {
      transform: '@mui/icons-material/{{member}}',
    },
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  trailingSlash: true,
  transpilePackages: ['crypto-js'],
  async headers() {
    return [
      {
        // Apply these headers to all routes in your application.
        source: '/:path*',
        headers: securityHeaders,
      },
    ]
  },
  images: {
    minimumCacheTTL: 300,
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**.amazonaws.com',
      },
      {
        protocol: 'https',
        hostname: '**.sandbox.yougotagift.com',
      },
      {
        protocol: 'https',
        hostname: '**.yougotagift.com',
      },
      {
        protocol: 'https',
        hostname: '**.cameratag.com',
      },
      {
        protocol: 'https',
        hostname: '**.giphy.com',
      },
      {
        protocol: 'https',
        hostname: 'lh3.googleusercontent.com',
      },
      {
        protocol: 'https',
        hostname: 'ygag-ecom-cognito-qa-tf.s3.us-east-2.amazonaws.com',
      },
      {
        protocol: 'https',
        hostname: 'ygag-ecom-cognito-sandbox-tf.s3.ap-south-1.amazonaws.com',
      },
      {
        protocol: 'https',
        hostname: 'ygag-ecom-cognito-production-1-tf.s3.ap-south-1.amazonaws.com',
      },
    ],
  },
  sentry: {
    widenClientFileUpload: true,
    hideSourceMaps: true,
	},
};

const sentryWebpackPluginOptions = {
  url: process.env.SENTRY_URL,
  org: process.env.SENTRY_ORG,
  project: process.env.SENTRY_PROJECT,
  authToken: process.env.SENTRY_AUTH_TOKEN,
	silent: false, // Suppresses all logs
	// For all available options, see:
	// https://github.com/getsentry/sentry-webpack-plugin#options.
  };

module.exports = withSentryConfig(nextConfig, sentryWebpackPluginOptions);
